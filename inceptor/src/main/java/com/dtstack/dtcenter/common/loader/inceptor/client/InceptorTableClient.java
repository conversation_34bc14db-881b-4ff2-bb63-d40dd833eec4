/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.inceptor.client;

import com.dtstack.dtcenter.common.loader.inceptor.InceptorConnFactory;
import com.dtstack.dtcenter.common.loader.rdbms.AbsTableClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.UpsertColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * inceptor table client
 *
 * <AUTHOR>
 * date：Created in 下午2:19 2021/5/6
 * company: www.dtstack.com
 */
public class InceptorTableClient extends AbsTableClient {

    private static final String ADD_COLUMN_SQL = "alter table %s add columns(%s %s comment '%s')";

    private static final String TABLE_IS_VIEW_SQL = "desc formatted %s";

    @Override
    protected ConnFactory getConnFactory() {
        return new InceptorConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.INCEPTOR;
    }

    private static final String DESC_FORMATTED_SQL = "desc formatted %s";

    @Override
    public Boolean isView(ISourceDTO source, String schema, String tableName) {
        checkParamAndSetSchema(source, schema, tableName);
        String sql = String.format(DESC_FORMATTED_SQL, tableName);
        List<Map<String, Object>> result = executeQuery(source, sql);
        if (CollectionUtils.isEmpty(result)) {
            throw new DtLoaderException(String.format("Execute to determine whether the table is a view sql result is empty，sql：%s", sql));
        }
        String tableType = "";
        for (Map<String, Object> row : result) {
            String colName = MapUtils.getString(row, "category", "");
            if (StringUtils.containsIgnoreCase(colName, "Type")) {
                tableType = MapUtils.getString(row, "attribute");
                break;
            }
        }
        return StringUtils.containsIgnoreCase(tableType, "VIEW");
    }

    /**
     * 添加表字段
     *
     * @param source
     * @param columnMetaDTO
     * @return
     */
    protected Boolean addTableColumn(ISourceDTO source, UpsertColumnMetaDTO columnMetaDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        String schema = StringUtils.isNotBlank(columnMetaDTO.getSchema()) ? columnMetaDTO.getSchema() : rdbmsSourceDTO.getSchema();
        String comment = StringUtils.isNotBlank(columnMetaDTO.getColumnComment()) ? columnMetaDTO.getColumnComment() : "";
        String sql = String.format(ADD_COLUMN_SQL, transferSchemaAndTableName(schema, columnMetaDTO.getTableName()), columnMetaDTO.getColumnName(), columnMetaDTO.getColumnType(), comment);
        return executeSqlWithoutResultSet(source, sql);
    }

    @Override
    public Long getTableSize(ISourceDTO source, String schema, String tableName) {
        String sql = String.format(TABLE_IS_VIEW_SQL, tableName);
        List<Map<String, Object>> result = executeQuery(source, sql);
        if (CollectionUtils.isEmpty(result)) {
            throw new DtLoaderException(String.format("Execute to determine whether the table size sql result is empty，sql：%s", sql));
        }
        for (Map<String, Object> row : result) {
            String tableType = MapUtils.getString(row, "data_type");
            if (StringUtils.containsIgnoreCase(tableType, "totalSize")) {
                try {
                    String comment = MapUtils.getString(row, "comment");
                    return comment != null ? Long.valueOf(comment.trim()) : 0L;
                } catch (Exception e) {
                    e.printStackTrace();
                    break;
                }
            }
        }
        return 0L;
    }
}
