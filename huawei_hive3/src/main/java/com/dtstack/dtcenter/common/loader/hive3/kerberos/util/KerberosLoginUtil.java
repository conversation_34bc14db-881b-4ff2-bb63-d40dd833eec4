/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hive3.kerberos.util;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.dtcenter.common.loader.common.DtClassThreadFactory;
import com.dtstack.dtcenter.common.loader.hive3.kerberos.hdfs.HadoopConfUtil;
import com.dtstack.dtcenter.loader.dto.source.HuaweiHive3SourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hadoop.security.authentication.util.JaasConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.security.krb5.Config;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:59 2020/9/1
 * @Description：Kerberos 登录相关操作
 */
public class KerberosLoginUtil {
    private static final Logger log = LoggerFactory.getLogger(KerberosLoginUtil.class);
    /**
     * Kerberos 默认角色配置信息
     */
    private static final String SECURITY_TO_LOCAL = "hadoop.security.auth_to_local";
    private static final String SECURITY_TO_LOCAL_DEFAULT = "RULE:[1:$1] RULE:[2:$1]";

    /* zookeeper节点ip和端口列表 */
    private static String zkQuorum = null;
    private static String auth = null;
    private static String sasl_qop = null;
    private static String zooKeeperNamespace = null;
    private static String serviceDiscoveryMode = null;
    private static String principal = null;
    private static String AUTH_HOST_NAME = null;
    private static String USER_NAME = null;

    private static String KRB5_FILE = null;
    private static String USER_KEYTAB_FILE = null;

    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";
    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";
    private static String ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL = null;

    private static ConcurrentHashMap<String, UGICacheData> UGI_INFO = new ConcurrentHashMap<>();

    private static final ScheduledExecutorService SCHEDULED_THREAD_POOL_EXECUTOR = new ScheduledThreadPoolExecutor(1, new DtClassThreadFactory("ugiCacheFactory"));

    static {
        SCHEDULED_THREAD_POOL_EXECUTOR.scheduleAtFixedRate(new CacheTimerTask(), 0, 10, TimeUnit.SECONDS);
    }

    static class CacheTimerTask implements Runnable {
        @Override
        public void run() {
            Iterator<String> iterator = UGI_INFO.keySet().iterator();
            while (iterator.hasNext()) {
                clearKey(iterator.next());
            }
        }

        private void clearKey(String principal) {
            UGICacheData ugiCacheData = UGI_INFO.get(principal);
            if (ugiCacheData == null || ugiCacheData.getUgi() == null) {
                UGI_INFO.remove(principal);
                log.info("KerberosLogin CLEAR UGI {}", principal);
                return;
            }

            if (System.currentTimeMillis() > ugiCacheData.getTimeoutStamp()) {
                UGI_INFO.remove(principal);
                log.info("KerberosLogin CLEAR UGI {}", principal);
            }
        }
    }


    public static UserGroupInformation loginWithUGIForHuaWei(HuaweiHive3SourceDTO hive3SourceDTO) {
        return loginWithUGIAllForHuaWei(hive3SourceDTO);
    }


    public static UserGroupInformation loginWithUGIAllForHuaWei(HuaweiHive3SourceDTO hive3SourceDTO) {
        synchronized (DataSourceType.class) {
            log.info("lailelaodi");
            Configuration conf = HadoopConfUtil.getHdfsConf(hive3SourceDTO.getDefaultFS(), hive3SourceDTO.getConfig(), hive3SourceDTO.getKerberosConfig());
            log.info("conf,{}", conf.toString());
            //获取hive.perpores属性
            zkQuorum = String.valueOf(conf.get("zk.quorum"));
            log.info("[zkQuorum]当前获得,zkQuorum，{}", zkQuorum);
            auth = String.valueOf(conf.get("auth"));
            log.info("[auth]当前获得,auth，{}", auth);
            sasl_qop = String.valueOf(conf.get("sasl.qop"));
            log.info("[sasl_qop]当前获得,sasl_qop，{}", sasl_qop);
            zooKeeperNamespace = String.valueOf(conf.get("zooKeeperNamespace"));
            log.info("[zooKeeperNamespace]当前获得,zooKeeperNamespace，{}", zooKeeperNamespace);
            serviceDiscoveryMode = String.valueOf(conf.get("serviceDiscoveryMode"));
            principal = String.valueOf(conf.get("principal"));
            log.info("[principal]当前获得,principal，{}", principal);
            // 设置新建用户的USER_NAME，其中"xxx"指代之前创建的用户名，例如创建的用户为user，则USER_NAME为user
            USER_NAME =hive3SourceDTO.getAuthUser();
            log.info("[hive3SourceDTO]当前获得,USER_NAME，{}", USER_NAME);
            UserGroupInformation userGroupInformation = null;
            log.info("[hive3SourceDTO]当前获得,auth，{}", auth);
            if ("KERBEROS".equalsIgnoreCase(auth)) {
                // 设置客户端的keytab和krb5文件路径
                USER_KEYTAB_FILE = MapUtils.getString(hive3SourceDTO.getKerberosConfig(), HadoopConfTool.PRINCIPAL_FILE);
                KRB5_FILE = MapUtils.getString(hive3SourceDTO.getKerberosConfig(), HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL = "zookeeper/" + getUserRealm();
                log.error("mrs的hive 的zk kerberos 需要写 jaas 文件，ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL:{}",ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL);
                try {
                    log.info("我用了新的内容");
                    LoginUtil.setJaasConf(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME, USER_NAME, USER_KEYTAB_FILE);
                    log.info("我结束了setJaasConf");
                    LoginUtil.setZookeeperServerPrincipal(ZOOKEEPER_SERVER_PRINCIPAL_KEY, ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL);
                    log.info("我结束了setJaasConf setZookeeperServerPrincipal");
                    userGroupInformation = LoginUtil.login(USER_NAME, USER_KEYTAB_FILE, KRB5_FILE, conf);
                    log.info("我结束了setJaasConf login");
                    LoginUtil.processZkSsl(conf);
                    log.info("我结束了 processZkSsl");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                log.info("当前获得,ZOOKEEPER_SERVER_PRINCIPAL_KEY，{}", ZOOKEEPER_SERVER_PRINCIPAL_KEY);
            }
            StringBuilder strBuilder = new StringBuilder("jdbc:hive2://").append(zkQuorum).append("/");
            log.info("我结束了 hive3SourceDTO.getSchema(),{}",hive3SourceDTO.getSchema());

            if(null != hive3SourceDTO.getSchema()
                    && !"".equals(hive3SourceDTO.getSchema())
                    && !"null".equalsIgnoreCase(hive3SourceDTO.getSchema())){
                log.info("hive的schema不为空，添加到url，{}",hive3SourceDTO.getSchema());
                strBuilder.append(hive3SourceDTO.getSchema());
            }else{
                log.info("开始截取url的database,{}",hive3SourceDTO.getUrl());
                String schema = getDatabaseName(hive3SourceDTO.getUrl());
                log.info("开始截取url的database-schema,{}",schema);
                if(null != schema && !"".equals(schema) && !"null".equalsIgnoreCase(schema)){
                    strBuilder.append(schema);
                }

            }
            log.info("结束 拼完了 schema ，{}'",strBuilder.toString());
            //拼接url
            if ("KERBEROS".equalsIgnoreCase(auth)) {
                strBuilder
                        .append(";serviceDiscoveryMode=")
                        .append(serviceDiscoveryMode)
                        .append(";zooKeeperNamespace=")
                        .append(zooKeeperNamespace)
                        .append(";sasl.qop=")
                        .append(sasl_qop)
                        .append(";auth=")
                        .append(auth)
                        .append(";principal=")
                        .append(principal)
                        .append(";user.principal=")
                        .append(USER_NAME)
                        .append(";user.keytab=")
                        .append(USER_KEYTAB_FILE)
                        .append(";");
            } else {
                /* 普通模式 */
                strBuilder
                        .append(";serviceDiscoveryMode=")
                        .append(serviceDiscoveryMode)
                        .append(";zooKeeperNamespace=")
                        .append(zooKeeperNamespace)
                        .append(";auth=none");
            }
            log.info("login kerberos success, url={}", strBuilder.toString());
            hive3SourceDTO.setUrl(strBuilder.toString());
            try {
                return userGroupInformation;
            } catch (Exception var6) {
                throw new DtLoaderException("login kerberos failed", var6);
            }
        }
    }


    /**
     * Get user realm process
     */
    public static String getUserRealm() {
        String serverRealm = System.getProperty("SERVER_REALM");
        if (serverRealm != null && serverRealm != "") {
            AUTH_HOST_NAME = "hadoop." + serverRealm.toLowerCase();
        } else {
            serverRealm = getKrb5DomainRealm();
            if (serverRealm != null && serverRealm != "") {
                AUTH_HOST_NAME = "hadoop." + serverRealm.toLowerCase();
            } else {
                AUTH_HOST_NAME = "hadoop";
            }
        }
        return AUTH_HOST_NAME;
    }


    /**
     * JAVA_VENDER
     */
    public static final String JAVA_VENDER = "java.vendor";
    /**
     * IBM_FLAG
     */
    public static final String IBM_FLAG = "IBM";
    /**
     * CONFIG_CLASS_FOR_IBM
     */
    public static final String CONFIG_CLASS_FOR_IBM = "com.ibm.security.krb5.internal.Config";
    /**
     * CONFIG_CLASS_FOR_SUN
     */
    public static final String CONFIG_CLASS_FOR_SUN = "sun.security.krb5.Config";
    /**
     * METHOD_GET_INSTANCE
     */
    public static final String METHOD_GET_INSTANCE = "getInstance";
    /**
     * METHOD_GET_DEFAULT_REALM
     */
    public static final String METHOD_GET_DEFAULT_REALM = "getDefaultRealm";
    /**
     * DEFAULT_REALM
     */
    public static final String DEFAULT_REALM = "HADOOP.COM";


    /**
     * Get Krb5 Domain Realm
     */
    public static String getKrb5DomainRealm() {
        Class<?> krb5ConfClass;
        String peerRealm = null;
        try {
            if (System.getProperty(JAVA_VENDER).contains(IBM_FLAG)) {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_IBM);
            } else {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_SUN);
            }

            Method getInstanceMethod = krb5ConfClass.getMethod(METHOD_GET_INSTANCE);
            Object kerbConf = getInstanceMethod.invoke(krb5ConfClass);

            Method getDefaultRealmMethod = krb5ConfClass.getDeclaredMethod(METHOD_GET_DEFAULT_REALM);
            if (getDefaultRealmMethod.invoke(kerbConf) instanceof String) {
                peerRealm = (String) getDefaultRealmMethod.invoke(kerbConf);
            }
            log.info("Get default realm successfully, the realm is : {}", peerRealm);

        } catch (ClassNotFoundException e) {
            peerRealm = DEFAULT_REALM;
            log.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            peerRealm = DEFAULT_REALM;
            log.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        }

        return peerRealm;
    }


    /**
     * 获取dbName
     * @param jdbcUrl
     * @return
     */
    public static String getDatabaseName(String jdbcUrl) {
        String dbName ="";

        // Patterns for various JDBC URL formats
        String[] patterns = {
                "/([^:/?;]+)(\\?|$)",                    // General pattern for dbName after '/'
                ";libraries=([^;?]+)",                   // AS400 pattern
                ";DatabaseName=([^;?]+)",                // SQL Server / Greenplum pattern (with possible ?)
                ":INFORMIXSERVER=[^:]+:([^:/?;]+)",      // Informix pattern
                "@//[^:]+:\\d+/([^:/?;]+)",              // Oracle thin pattern
                "/([^;?]+)[;?]?",                // 匹配 `/dbName`，可能跟有 `;` 或 `?`
                ":([a-zA-Z0-9_]+)(;|$)"          // 匹配 `:dbName`，后面可能是 `;` 或字符串的结尾
        };
        // Iterate over the patterns to find a match
        for (String pattern : patterns) {
            Matcher matcher = Pattern.compile(pattern).matcher(jdbcUrl);
            if (matcher.find()) {
                dbName = matcher.group(1);
                break;
            }
        }

        return dbName;
    }
}
