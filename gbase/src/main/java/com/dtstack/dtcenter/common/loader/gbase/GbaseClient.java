/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.gbase;

import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.GBaseSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:57 2020/1/7
 * @Description：GBase8a 客户端
 */
public class GbaseClient extends AbsRdbmsClient {

    /**
     * 获取当前版本号
     */
    private static final String SHOW_VERSION = "select version()";

    /**
     * 获取表字段注释
     */
    private static final String COLUMN_COMMENT = "SHOW FULL COLUMNS FROM %s.%s";

    /**
     * 判断数据库是否存在
     */
    private static final String DATABASE_EXISTS = "SHOW DATABASES LIKE '%s'";

    /**
     *  查看当前数据库
     * @return
     */
    private static final String CUR_DATABASE = "select database()";

    @Override
    protected ConnFactory getConnFactory() {
        return new GbaseConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.GBase_8a;
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GBaseSourceDTO gBaseSourceDTO = (GBaseSourceDTO) iSource;
        Map<String, String> databaseParameters = getTableStatus(gBaseSourceDTO, queryDTO);
        return databaseParameters.get(DtClassConsistent.PublicConsistent.collation);
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    public String getCharacterSet(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return "";
    }

    @Override
    public String getCharacterCollation(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Map<String, String> databaseParameters = getTableStatus(sourceDTO, queryDTO);
        return databaseParameters.get(DtClassConsistent.PublicConsistent.collation);
    }

    public Map<String, String> getTableStatus(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        GBaseSourceDTO gBaseSourceDTO = (GBaseSourceDTO) iSource;
        Statement statement = null;
        ResultSet resultSet = null;
        Map<String, String> tableParameters = new HashMap<>();
        try {
            statement = gBaseSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery("show table status");
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);

                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    tableParameters.put(DtClassConsistent.PublicConsistent.COMMENT, resultSet.getString("Comment"));
                    tableParameters.put(DtClassConsistent.PublicConsistent.collation, resultSet.getString("Collation"));
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.%s",
                    queryDTO.getTableName(), e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gBaseSourceDTO, clearStatus));
        }
        return tableParameters;
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(DATABASE_EXISTS, dbName)).build()));
    }

    @Override
    protected String getCurrentDbSql() {
        return CUR_DATABASE;
    }


    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

}
