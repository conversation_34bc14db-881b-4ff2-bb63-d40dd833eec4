/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.kingbase;

import com.dsg.database.datasource.dto.ProcedureMetadata;
import com.dsg.database.datasource.dto.ProcedureMetadataArguments;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.SearchUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KingbaseSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.PostgresqlSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;

/**
 * company: www.dtstack.com
 *
 * <AUTHOR>
 * Date ：Created in 17:18 2020/09/01
 * Description：kingbase 客户端
 */
public class KingbaseClient extends AbsRdbmsClient {

    /**
     * 获取所有schema，去除系统库
     */
    private static final String SCHEMA_SQL = "SELECT NSPNAME FROM SYS_CATALOG.SYS_NAMESPACE WHERE NSPNAME !~ 'sys' AND NSPNAME <> 'information_schema' ORDER BY NSPNAME ";

    /**
     * 获取某个schema下的所有表
     */
    private static final String SCHEMA_TABLE_SQL = "SELECT tablename FROM SYS_CATALOG.sys_tables WHERE schemaname = '%s' %s";

    /**
     * 获取所有表名，表名前拼接schema，并对schema和tableName进行增加双引号处理
     */
    private static final String ALL_TABLE_SQL = "SELECT '\"'||schemaname||'\".\"'||tablename||'\"' AS schema_table FROM SYS_CATALOG.sys_tables WHERE 1=1 %s";

    /**
     * 获取某个表的表注释信息
     */
    private static final String TABLE_COMMENT_SQL = "SELECT COMMENTS FROM ALL_TAB_COMMENTS WHERE TABLE_NAME = '%s' ";

    /**
     * 获取某个表的字段注释信息
     */
    private static final String COL_COMMENT_SQL = "SELECT COLUMN_NAME,COMMENTS FROM ALL_COL_COMMENTS WHERE TABLE_NAME = '%s' ";

    /**
     * 获取正在使用数据库
     */
    private static final String CURRENT_DB = "select current_database()";

    /**
     * 获取正在使用 schema
     */
    private static final String CURRENT_SCHEMA = "select current_schema()";

    private static final String DONT_EXIST = "doesn't exist";

    /**
     * 根据schema选表表名模糊查询
     */
    private static final String SEARCH_SQL = " AND tablename LIKE '%s' ";

    /**
     * 限制条数语句
     */
    private static final String LIMIT_SQL = " LIMIT %s ";

    /**
     * 获取当前版本号
     */
    private static final String SHOW_VERSION = "select version()";

    /**
     * 获取数据库字符集
     *
     * @return
     */
    private static final String SHOW_CHARACTER = "SELECT pg_encoding_to_char(encoding) AS character_set FROM pg_database WHERE datname = '%s'";

    private static final String TABLE_ROWS = "select count(1) from %s.\"%s\"";

    private static final String SCHEMA_COLLATION_NAME = "SELECT c.collname AS collation_name\n" +
            "FROM pg_collation c\n" +
            "JOIN pg_namespace n ON c.collnamespace = n.oid\n" +
            "WHERE n.nspname = '%s';";

    @Override
    protected ConnFactory getConnFactory() {
        return new KingbaseConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.KINGBASE8;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableListBySchema(source, queryDTO);
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String schema = queryDTO.getSchema();
            if (StringUtils.isEmpty(schema)) {
                schema = super.getCurrentSchema(iSource);
            }
            resultSet = statement.executeQuery(String.format(TABLE_ROWS, schema, queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return tableRow;
    }

    /**
     * 获取表注释信息
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public String getTableMetaComment(ISourceDTO source, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(TABLE_COMMENT_SQL, queryDTO.getTableName()));
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to get the information of table: %s. Please contact DBA to check the database and table information: %s",
                    queryDTO.getTableName(), e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

    /**
     * 处理kingbase schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("\"") || !tableName.endsWith("\"")) {
            tableName = String.format("\"%s\"", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("\"") || !schema.endsWith("\"")) {
            schema = String.format("\"%s\"", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    /**
     * 获取所有 数据库/schema sql语句
     *
     * @return
     */
    @Override
    protected String getShowDbSql() {
        return SCHEMA_SQL;
    }

    /**
     * 获取字段注释
     *
     * @param sourceDTO
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(String.format(COL_COMMENT_SQL, queryDTO.getTableName()));
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String columnComment = rs.getString("COMMENTS");
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to get the comment information of the field of the table: %s. Please contact the DBA to check the database and table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    @Override
    public List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(source, queryDTO);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Statement statement = null;
        ResultSet rs = null;
        List<ColumnMetaDTO> columns = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String queryColumnSql = "select * from " + transferSchemaAndTableName(kingbaseSourceDTO, queryDTO)
                    + " where 1=2";
            rs = statement.executeQuery(queryColumnSql);
            ResultSetMetaData rsMetaData = rs.getMetaData();
            for (int i = 0, len = rsMetaData.getColumnCount(); i < len; i++) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(rsMetaData.getColumnName(i + 1));
                String type = rsMetaData.getColumnTypeName(i + 1);
                int columnType = rsMetaData.getColumnType(i + 1);
                int precision = rsMetaData.getPrecision(i + 1);
                int scale = rsMetaData.getScale(i + 1);
                //kingbase类型转换
                String flinkSqlType = KingbaseAdapter.mapColumnTypeJdbc2Java(columnType, precision, scale);
                if (StringUtils.isNotEmpty(flinkSqlType)) {
                    type = flinkSqlType;
                }
                columnMetaDTO.setType(type);
                // 获取字段精度
                if (columnMetaDTO.getType().equalsIgnoreCase("decimal")
                        || columnMetaDTO.getType().equalsIgnoreCase("float")
                        || columnMetaDTO.getType().equalsIgnoreCase("double")
                        || columnMetaDTO.getType().equalsIgnoreCase("numeric")) {
                    columnMetaDTO.setScale(rsMetaData.getScale(i + 1));
                    columnMetaDTO.setPrecision(rsMetaData.getPrecision(i + 1));
                }
                columns.add(columnMetaDTO);
            }
            return columns;

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get meta information for the fields of table :%s. Please contact the DBA to check the database table information.", queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }

    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCurrentSchemaSql() {
        return CURRENT_SCHEMA;
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            return String.format(ALL_TABLE_SQL, constr.toString());
        }
        return String.format(SCHEMA_TABLE_SQL, schema, constr.toString());
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        ResultSet resultSet = null;
        Statement statement = null;
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        try {
            String curDatabase = super.getCurrentDatabase(source);
            statement = source.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SHOW_CHARACTER, curDatabase));
            if (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public String getCharacterSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        ResultSet resultSet = null;
        Statement statement = null;
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        try {
            String curDatabase = super.getCurrentSchema(iSource);
            statement = iSource.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SCHEMA_COLLATION_NAME, curDatabase));
            StringBuilder collationNameBuilder = new StringBuilder();
            while (resultSet.next()) {
                collationNameBuilder.append(resultSet.getString(1)).append(",");
            }
            if (collationNameBuilder.length() > 0) {
                // 删除最后一个分隔符
                collationNameBuilder.deleteCharAt(collationNameBuilder.length() - 1);
            }
            return collationNameBuilder.toString();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

    /**
     * 执行查询
     *
     * @param rdbmsSourceDTO
     * @param queryDTO
     * @param clearStatus
     * @return
     * @throws SQLException
     */
    public List<Map<String, Object>> executeQuery(RdbmsSourceDTO rdbmsSourceDTO, SqlQueryDTO queryDTO, Integer clearStatus) {
        try {
            Boolean setMaxRow = null;
            if (null != queryDTO.getLimit() && queryDTO.getLimit() >= 0) {
                if (StringUtils.isNotBlank(queryDTO.getSql()) && !queryDTO.getSql().contains(" limit ")) {
                    queryDTO.setSql(queryDTO.getSql() + " limit " + queryDTO.getLimit());
                }
            }
            // 预编译字段
            if (queryDTO.getPreFields() != null) {
                return DBUtil.executeQuery(rdbmsSourceDTO.getConnection(), queryDTO.getSql(), queryDTO.getLimit(), queryDTO.getPreFields(), queryDTO.getQueryTimeout(), setMaxRow, this::dealResult);
            }
            return DBUtil.executeQuery(rdbmsSourceDTO.getConnection(), queryDTO.getSql(), queryDTO.getLimit(), queryDTO.getQueryTimeout(), setMaxRow, this::dealResult);
        }catch (Exception e){
            throw new DtLoaderException(String.format("SQL executed exception, %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        return executeQuery(rdbmsSourceDTO, queryDTO, clearStatus);
    }


    /**
     * rdbms数据预览
     *
     * @param iSource
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        List<List<Object>> previewList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return previewList;
        }
        Statement stmt = null;
        ResultSet rs = null;
        PreparedStatement preparedStatement = null;
        try {
            stmt = rdbmsSourceDTO.getConnection().createStatement();
            //查询sql，默认预览100条
            String querySql = dealSql(rdbmsSourceDTO, queryDTO);
            String whereSql = dealWhereSql(rdbmsSourceDTO, querySql, queryDTO);
            querySql = querySql + whereSql;
            if (queryDTO.getPreviewNum() != null) {
                int endIndex = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                if (pageNum != null) {
                    endIndex = pageNum * endIndex;
                }
                stmt.setMaxRows(endIndex);
                if (queryDTO.getPreviewNum() >= 0) {
                    if (StringUtils.isNotBlank(querySql) && !querySql.contains(" limit ")) {
                        querySql = querySql + " limit " + queryDTO.getPreviewNum();
                    }
                }
            }
            rs = stmt.executeQuery(querySql);
            if (queryDTO.getPreviewNum() != null) {
                int previewNum = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                if (pageNum != null) {
                    // 查询分页数据
                    int beginIndex = (pageNum - 1) * previewNum;
                    if (beginIndex > 0) {
                        // 设置读取数据的起始位置
                        rs.absolute(beginIndex);
                    }
                }
            }
            ResultSetMetaData rsmd = rs.getMetaData();
            //存储字段信息
            List<Object> metaDataList = Lists.newArrayList();
            //字段数量
            int len = rsmd.getColumnCount();
            for (int i = 0; i < len; i++) {
                metaDataList.add(rsmd.getColumnLabel(i + 1));
            }
            while (rs.next()) {
                //一个columnData存储一行数据信息
                ArrayList<Object> columnData = Lists.newArrayList();
                for (int i = 0; i < len; i++) {
                    String result = dealPreviewResult(rs.getObject(i + 1));
                    columnData.add(result);
                }
                previewList.add(columnData);
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return previewList;
    }


    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : kingbaseSourceDTO.getSchema();
        List<Map<String, Object>> columnResult;
        List<Map<String, Object>> constraintResult;
        String tableName;
        if (StringUtils.isNotBlank(schema)) {
            try {
                columnResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_COLUMN_BY_SCHEMA, queryDTO.getTableName(), schema)).build());
                constraintResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_CONSTRAINT_BY_SCHEMA_NEW, queryDTO.getTableName(), schema)).build());
                tableName = String.format(SqlConstants.SCHEMA_TABLE_FORMAT, schema, queryDTO.getTableName());
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            }
        } else {
            try {
                columnResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_COLUMN, queryDTO.getTableName())).build());
                constraintResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_CONSTRAINT, queryDTO.getTableName())).build());
                tableName = queryDTO.getTableName();
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            }
        }
        if (CollectionUtils.isEmpty(columnResult) || StringUtils.isBlank(MapUtils.getString(columnResult.get(0), "column"))) {
            throw new DtLoaderException(String.format("Failed to get table %s field", queryDTO.getTableName()));
        }
        String columnStr = MapUtils.getString(columnResult.get(0), "column");
        String constraint = null;
        if (CollectionUtils.isNotEmpty(constraintResult)) {
            constraint = MapUtils.getString(constraintResult.get(0), "constraint");
        }
        if (StringUtils.isNotBlank(constraint)) {
            return String.format(SqlConstants.CREATE_TABLE_TEMPLATE, tableName, columnStr + " , " + constraint);
        }
        return String.format(SqlConstants.CREATE_TABLE_TEMPLATE, tableName, columnStr);
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) iSource;
        ResultSet rs = null;
        List<TableViewDTO> tableList = new ArrayList<>();
        Statement statement = null;
        ResultSet rs_p = null;
        try {
            DatabaseMetaData meta = kingbaseSourceDTO.getConnection().getMetaData();
            if (null == queryDTO) {
                rs = meta.getTables(null, null, null, new String[]{"TABLE", "VIEW"});
            } else {
                if (BooleanUtils.isTrue(queryDTO.getView())) {
                    rs = meta.getTables(null, queryDTO.getSchema(), null, new String[]{"TABLE", "VIEW"});
                } else {
                    rs = meta.getTables(null, queryDTO.getSchema(), null, new String[]{"TABLE"});
                }
            }
            while (rs.next()) {
                TableViewDTO tableViewDTO = new TableViewDTO(rs.getString(3), rs.getString(4));
                tableList.add(tableViewDTO);
            }
            //todo 获取所有存储过程
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String sql=String.format(SqlConstants.ALL_PROCEDURES, queryDTO.getSchema());
//            log.info("sql:{}",sql);
            rs_p = statement.executeQuery(sql);
            while (rs_p.next()) {
                String objectName = rs_p.getString("specific_name");
                String objectType = rs_p.getString("ROUTINE_TYPE");
                TableViewDTO tableViewDTO = new TableViewDTO(objectName,objectType);
                tableList.add(tableViewDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get database tableView exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs_p, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimitForTableAndView(tableList, queryDTO);
    }


    /**
     * 获取存储过程
     *
     */
    @Override
    public ProcedureMetadata getProduce(ISourceDTO source, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        ProcedureMetadata metadata = new ProcedureMetadata();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(), queryDTO.getObjectName());
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
//            log.info("getProduce sql:{}",sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                //根据规则生成一个数字类型的id
                metadata.setProcedureId(Long.valueOf(generateNumericId(queryDTO.getObjectName(), DataSourceTypeEnum.KINGBASE8.getDataType())));
                metadata.setObjectName(resultSet.getString("ROUTINE_NAME"));
                metadata.setObjectType(resultSet.getString("ROUTINE_TYPE"))  ;
                metadata.setStatus("VALID") ;
                metadata.setCreated(resultSet.getDate("CREATED"));
                metadata.setLastDdlTime(resultSet.getDate("LAST_ALTERED"));
                metadata.setSourceCode(resultSet.getString("ROUTINE_DEFINITION"));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return metadata;
    }
    public  int generateNumericId(String objectName, String dataSourceType) {
        int hash = objectName.hashCode() + dataSourceType.hashCode();
        // 安全处理 Integer.MIN_VALUE 的情况
        int safe = (hash == Integer.MIN_VALUE) ? 0 : Math.abs(hash);
        return safe;
    }
    /**
     * 获取存储过程
     *
     */
    @Override
    public List<ProcedureMetadataArguments> getProduceArguments(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        ResultSet rs = null;
        Statement statement = null;
        List<ProcedureMetadataArguments> arguments = new ArrayList<>();
        try {
            //todo 获取所有存储过程
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String sql=String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL,queryDTO.getSchema(), queryDTO.getObjectName());
//            log.info("getProduceArguments sql:{}",sql);
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                ProcedureMetadataArguments argument = new ProcedureMetadataArguments();
                argument.setArgumentName(rs.getString("parameter_name"));
                argument.setPosition(Long.valueOf(rs.getInt("ordinal_position")));
                argument.setDataType(rs.getString("DATA_TYPE"))  ;
                argument.setInOut(rs.getString("parameter_mode"));
                argument.setDataLength(Long.valueOf(rs.getInt("character_maximum_length")));
                arguments.add(argument);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get ProduceArguments  exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return arguments;
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(SqlConstants.TABLE_BY_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }

}
