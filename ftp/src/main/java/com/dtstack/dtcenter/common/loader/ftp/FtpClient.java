/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.ftp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.Base64DatasourceJson;
import com.dsg.database.datasource.dto.DatasourceInfoDTO;
import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.ParseDatasourceUtils;
import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.common.loader.common.utils.AddressUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.FtpSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.jcraft.jsch.JSchException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 22:52 2020/2/27
 * @Description：FTP 客户端
 */
@Slf4j
public class FtpClient<T> extends AbsNoSqlClient<T> {

    @Override
    public Boolean testCon(ISourceDTO sourceDTO) {
        boolean isTrue = false;
        try {
            FtpSourceDTO ftpSourceDTO = (FtpSourceDTO) sourceDTO;
            Integer port = FtpUtil.getFtpPort(ftpSourceDTO.getProtocol(), ftpSourceDTO.getHostPort());
            if (!AddressUtil.telnet(ftpSourceDTO.getUrl(), port)) {
                return Boolean.FALSE;
            }
            if (StringUtils.equalsIgnoreCase(ProtocolEnum.SFTP.name(), ftpSourceDTO.getProtocol())) {
                SFTPHandler sftpHandler = null;
                try {
                    sftpHandler = FtpClientFactory.getSFTPHandler(ftpSourceDTO);
                } finally {
                    if (Objects.nonNull(sftpHandler)) {
                        sftpHandler.close();
                    }
                }
            } else if (StringUtils.equalsIgnoreCase(ProtocolEnum.FTP.name(), ftpSourceDTO.getProtocol())) {
                FTPClient ftpClient = null;
                try {
                    ftpClient = FtpClientFactory.getFtpClient(ftpSourceDTO);
                } finally {
                    if (Objects.nonNull(ftpClient)) {
                        try {
                            ftpClient.disconnect();
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            } else {
                SSHHandler sshHandler = null;
                try {
                    sshHandler = FtpClientFactory.getSSHHandler(ftpSourceDTO);
                } finally {
                    if (Objects.nonNull(sshHandler)) {
                        sshHandler.close();
                    }
                }
            }
            isTrue = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isTrue;
    }

    @Override
    public List<String> listFileNames(ISourceDTO sourceDTO, String path, Boolean includeDir, Boolean recursive, Integer maxNum, String regexStr) {
        FtpSourceDTO ftpSourceDTO = (FtpSourceDTO) sourceDTO;
        List<String> fileNames = null;
        if (StringUtils.equalsIgnoreCase(ProtocolEnum.SFTP.name(), ftpSourceDTO.getProtocol())) {
            SFTPHandler sftpHandler = null;
            try {
                sftpHandler = FtpClientFactory.getSFTPHandler(ftpSourceDTO);
                fileNames = FtpUtil.getSFTPFileNames(sftpHandler, path, includeDir, recursive, maxNum, regexStr);
            } finally {
                if (Objects.nonNull(sftpHandler)) {
                    sftpHandler.close();
                }
            }
        } else if (StringUtils.equalsIgnoreCase(ProtocolEnum.FTP.name(), ftpSourceDTO.getProtocol())) {
            FTPClient ftpClient = null;
            try {
                ftpClient = FtpClientFactory.getFtpClient(ftpSourceDTO);
                fileNames = FtpUtil.getFTPFileNames(ftpClient, path, includeDir, recursive, maxNum, regexStr);
            } finally {
                try {
                    if (Objects.nonNull(ftpClient)) {
                        ftpClient.disconnect();
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } else {
            SSHHandler sshHandler = null;
            try {
                sshHandler = FtpClientFactory.getSSHHandler(ftpSourceDTO);
                fileNames = sshHandler.executeCommand(path);
            } catch (JSchException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (Objects.nonNull(sshHandler)) {
                    sshHandler.close();
                }
            }
        }
        fileNames.sort(String::compareTo);
        return fileNames;
    }
    /**
     * 获取数据源动态导入
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public DatasourceInfoDTO getDataSourceImport(ISourceDTO source, SqlQueryDTO queryDTO){
        //获取导入对象
        DatasourceInfoImportVO datasourceInfoImportVO = queryDTO.getDatasourceInfoImportVO();
        //ftp 或者sftp 导入

        //获取枚举
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.typeVersionOf(datasourceInfoImportVO.getDataType(), datasourceInfoImportVO.getDataVersion());
        //构建加密字符串
        Base64DatasourceJson base64DatasourceJson = new Base64DatasourceJson();
        base64DatasourceJson.setDataName(datasourceInfoImportVO.getDataName());
        base64DatasourceJson.setDataTypeCode(dataSourceTypeEnum.getVal());
        base64DatasourceJson.setBusinessUuid(queryDTO.getBusinessUuid());
        base64DatasourceJson.setDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setShowDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setDriverClassName(dataSourceTypeEnum.getDriverClassName());
        base64DatasourceJson.setDataVersion(dataSourceTypeEnum.getDataVersion());
        base64DatasourceJson.setSchema(datasourceInfoImportVO.getSchema());
        String userName = datasourceInfoImportVO.getUserName();
        String passWord = datasourceInfoImportVO.getPassWord();
        base64DatasourceJson.setUsername(userName);
        base64DatasourceJson.setPassword(passWord);
        base64DatasourceJson.setProtocol(datasourceInfoImportVO.getDataType().toUpperCase());
        base64DatasourceJson.setHost(datasourceInfoImportVO.getIp());
        base64DatasourceJson.setPort(datasourceInfoImportVO.getPort());

        //设置数据源类型编码
        FtpSourceDTO ftpSourceDTO=(FtpSourceDTO)source;
        ftpSourceDTO.setPassword(passWord);
        ftpSourceDTO.setUsername(userName);
        ftpSourceDTO.setSourceType(dataSourceTypeEnum.getVal());
        ftpSourceDTO.setHostPort(datasourceInfoImportVO.getPort().toString());
        ftpSourceDTO.setUrl(datasourceInfoImportVO.getIp());
        ftpSourceDTO.setProtocol(datasourceInfoImportVO.getDataType().toUpperCase());
        Integer status = 0;
        try {
            //测试连接
            Boolean b = testCon(ftpSourceDTO);
            if (b) {
                //获取版本
                String version = "";
                try {
                    version = getVersion(ftpSourceDTO);
                } catch (Exception e) {
                    log.error("获取版本失败{}",datasourceInfoImportVO.getDataName());
                }
                base64DatasourceJson.setDbVersion(version);
                status=1;
            }
        } catch (Exception e) {
            log.error("[{}]数据源连接失败",datasourceInfoImportVO.getDataName());

        }
        //构建数据源对象
        String dataJson = JSONObject.toJSONString(base64DatasourceJson);
        //将json 转为对象
        DatasourceInfoDTO dto = JSON.parseObject(dataJson, DatasourceInfoDTO.class);
        dto.setStatus(status);
        //加密字符串
        dto.setDataJson(ParseDatasourceUtils.getEncodeDataSource(dataJson, true));
        dto.setDbName(datasourceInfoImportVO.getDbName());

        String dataDesc = datasourceInfoImportVO.getDataDesc();
        if(StringUtils.isNotEmpty(dataDesc)){
            dto.setDataDesc(dataDesc);
        }
        dto.setIp(datasourceInfoImportVO.getIp());
        return dto;
    }

}
