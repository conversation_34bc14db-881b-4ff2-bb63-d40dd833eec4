/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.ftp;

import com.dtstack.dtcenter.loader.dto.source.FtpSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

import java.util.Map;
import java.util.Optional;

/**
 * Ftp 客户端工厂
 *
 * <AUTHOR>
 * date：Created in 下午4:11 2021/6/21
 * company: www.dtstack.com
 */
public class FtpClientFactory {

    /**
     * 默认连接超时时间
     */
    private static final int TIMEOUT = 10000;

    /**
     * 获取 FTP 客户端
     *
     * @param ftpSourceDTO ftp 数据源连接信息
     * @return FTP 客户端
     */
    public static FTPClient getFtpClient(FtpSourceDTO ftpSourceDTO) {
        FTPClient ftpClient = new FTPClient();
        Integer port = FtpUtil.getFtpPort(ftpSourceDTO.getProtocol(), ftpSourceDTO.getHostPort());
        try {
            ftpClient.connect(ftpSourceDTO.getUrl(), port);
            ftpClient.login(ftpSourceDTO.getUsername(), ftpSourceDTO.getPassword());
            ftpClient.setConnectTimeout(TIMEOUT);
            ftpClient.setDataTimeout(TIMEOUT);
            if (StringUtils.equalsIgnoreCase(FTPConnectMode.PASV.name(), ftpSourceDTO.getConnectMode())) {
                ftpClient.enterRemotePassiveMode();
                ftpClient.enterLocalPassiveMode();
            } else if (StringUtils.equalsIgnoreCase(FTPConnectMode.PORT.name(), ftpSourceDTO.getConnectMode())) {
                ftpClient.enterLocalActiveMode();
            }
            int reply = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftpClient.disconnect();
                throw new DtLoaderException("Failed to establish a connection with the ftp server, please check whether the user name and password are correct");
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        }
        return ftpClient;
    }

    /**
     * 获取 SFTP client
     *
     * @param ftpSourceDTO sftp 数据源信息
     * @return SFTP client
     */
    public static SFTPHandler getSFTPHandler(FtpSourceDTO ftpSourceDTO) {
        try {
            Integer finalPort = FtpUtil.getFtpPort(ftpSourceDTO.getProtocol(), ftpSourceDTO.getHostPort());
            Map<String, String> sftpConfig = Maps.newHashMap();
            sftpConfig.put(SFTPHandler.KEY_HOST, ftpSourceDTO.getUrl());
            sftpConfig.put(SFTPHandler.KEY_PORT, String.valueOf(finalPort));
            sftpConfig.put(SFTPHandler.KEY_USERNAME, ftpSourceDTO.getUsername());
            sftpConfig.put(SFTPHandler.KEY_PASSWORD, ftpSourceDTO.getPassword());
            sftpConfig.put(SFTPHandler.KEY_TIMEOUT, String.valueOf(TIMEOUT));
            sftpConfig.put(SFTPHandler.KEY_AUTHENTICATION, Optional.ofNullable(ftpSourceDTO.getAuth()).orElse(""));
            sftpConfig.put(SFTPHandler.KEY_RSA, Optional.ofNullable(ftpSourceDTO.getPath()).orElse(""));
            return SFTPHandler.getInstance(sftpConfig);
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get sftp connection:%s", e.getMessage()));
        }
    }

    /**
     * 获取 SSH client
     *
     * @param ftpSourceDTO sftp 数据源信息
     * @return SFTP client
     */
    public static SSHHandler getSSHHandler(FtpSourceDTO ftpSourceDTO) {
        try {
            Integer finalPort = FtpUtil.getFtpPort(ftpSourceDTO.getProtocol(), ftpSourceDTO.getHostPort());
            Map<String, String> sftpConfig = Maps.newHashMap();
            sftpConfig.put(SFTPHandler.KEY_HOST, ftpSourceDTO.getUrl());
            sftpConfig.put(SFTPHandler.KEY_PORT, String.valueOf(finalPort));
            sftpConfig.put(SFTPHandler.KEY_USERNAME, ftpSourceDTO.getUsername());
            sftpConfig.put(SFTPHandler.KEY_PASSWORD, ftpSourceDTO.getPassword());
            sftpConfig.put(SFTPHandler.KEY_TIMEOUT, String.valueOf(TIMEOUT));
            sftpConfig.put(SFTPHandler.KEY_AUTHENTICATION, Optional.ofNullable(ftpSourceDTO.getAuth()).orElse(""));
            sftpConfig.put(SFTPHandler.KEY_RSA, Optional.ofNullable(ftpSourceDTO.getPath()).orElse(""));
            return SSHHandler.getInstance(sftpConfig);
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get ssh connection:%s", e.getMessage()));
        }
    }
}
