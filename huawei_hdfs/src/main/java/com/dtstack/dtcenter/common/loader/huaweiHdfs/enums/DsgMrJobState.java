package com.dtstack.dtcenter.common.loader.huaweiHdfs.enums;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2021/9/6
 * @create 2021-09-06-11:42
 */
public enum DsgMrJobState {
    RUNNING("RUNNING",1),
    SUCCEEDED("SUCCEEDED",2),
    FAILED("FAILED",3),
    PREP("PREP",4),
    KILLED("KILLED",5);

    private String type;

    private int state;

    DsgMrJobState(String type, int state) {
        this.type = type;
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "DsgMrJobState{" +
                "state='" + state + '\'' +
                ", type='" + type + '\'' +
                '}';
    }

    public static String getStateTypeName(Integer state) {
        for(DsgMrJobState dsgmrjobstate:  DsgMrJobState.values()) {
            if(dsgmrjobstate.getState() == state)
                return dsgmrjobstate.type;
        }
        return null;
    }
}
