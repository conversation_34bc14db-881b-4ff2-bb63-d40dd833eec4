/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.huaweiHdfs.kerberos;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.dtcenter.common.loader.common.DtClassThreadFactory;
import com.dtstack.dtcenter.loader.dto.source.HuaweiHdfsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.MapUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:59 2020/9/1
 * @Description：Kerberos 登录相关操作
 */
public class KerberosLoginUtil {
    private static final Logger log = LoggerFactory.getLogger(KerberosLoginUtil.class);
    /**
     * Kerberos 默认角色配置信息
     */
    private static final String SECURITY_TO_LOCAL = "hadoop.security.auth_to_local";
    private static final String SECURITY_TO_LOCAL_DEFAULT = "RULE:[1:$1] RULE:[2:$1]";

    /* zookeeper节点ip和端口列表 */
    private static String zkQuorum = null;
    private static String auth = null;
    private static String sasl_qop = null;
    private static String zooKeeperNamespace = null;
    private static String serviceDiscoveryMode = null;
    private static String principal = null;
    private static String AUTH_HOST_NAME = null;
    private static String USER_NAME = null;

    private static String KRB5_FILE = null;
    private static String USER_KEYTAB_FILE = null;

    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";
    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";
    private static String ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL = null;

    private static ConcurrentHashMap<String, UGICacheData> UGI_INFO = new ConcurrentHashMap<>();

    private static final ScheduledExecutorService SCHEDULED_THREAD_POOL_EXECUTOR = new ScheduledThreadPoolExecutor(1, new DtClassThreadFactory("ugiCacheFactory"));

    static {
        SCHEDULED_THREAD_POOL_EXECUTOR.scheduleAtFixedRate(new CacheTimerTask(), 0, 10, TimeUnit.SECONDS);
    }

    static class CacheTimerTask implements Runnable {
        @Override
        public void run() {
            Iterator<String> iterator = UGI_INFO.keySet().iterator();
            while (iterator.hasNext()) {
                clearKey(iterator.next());
            }
        }

        private void clearKey(String principal) {
            UGICacheData ugiCacheData = UGI_INFO.get(principal);
            if (ugiCacheData == null || ugiCacheData.getUgi() == null) {
                UGI_INFO.remove(principal);
                log.info("KerberosLogin CLEAR UGI {}", principal);
                return;
            }

            if (System.currentTimeMillis() > ugiCacheData.getTimeoutStamp()) {
                UGI_INFO.remove(principal);
                log.info("KerberosLogin CLEAR UGI {}", principal);
            }
        }
    }


    public static UserGroupInformation loginWithUGIForHuaWei(HuaweiHdfsSourceDTO huaweiHdfsSourceDTO) {
        return loginWithUGIAllForHuaWei(huaweiHdfsSourceDTO);
    }


    public static UserGroupInformation loginWithUGIAllForHuaWei(HuaweiHdfsSourceDTO huaweiHdfsSourceDTO) {
        synchronized (DataSourceType.class) {
            log.info("lailelaodi");
            Configuration conf = HadoopConfUtil.getHdfsConf(huaweiHdfsSourceDTO.getDefaultFS(), huaweiHdfsSourceDTO.getConfig(), huaweiHdfsSourceDTO.getKerberosConfig());
            log.info("conf,{}", conf.toString());

            // 设置新建用户的USER_NAME，其中"xxx"指代之前创建的用户名，例如创建的用户为user，则USER_NAME为user
            USER_NAME = String.valueOf(conf.get("principal"));
            log.info("[huaweiHdfsSourceDTO]当前获得,USER_NAME，{}", USER_NAME);
            UserGroupInformation userGroupInformation = null;
            // security mode
            String openKerberos = huaweiHdfsSourceDTO.getOpenKerberos();
            log.info("openKerberos,{}", openKerberos);
            if ("open".equalsIgnoreCase(openKerberos)) {
                log.info("[huaweiHdfsSourceDTO]开始进行kerberos认证");
                conf.set("hadoop.security.authentication","kerberos");
                USER_KEYTAB_FILE = MapUtils.getString(huaweiHdfsSourceDTO.getKerberosConfig(), HadoopConfTool.PRINCIPAL_FILE);
                KRB5_FILE = MapUtils.getString(huaweiHdfsSourceDTO.getKerberosConfig(), HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                System.setProperty("java.security.krb5.conf", KRB5_FILE);
                try {
                    log.info("[huaweiHdfsSourceDTO]开始登录，USER_NAME, USER_KEYTAB_FILE, KRB5_FILE,conf");
                    log.info("[huaweiHdfsSourceDTO]开始登录，{}, {}, {},{}",USER_NAME, USER_KEYTAB_FILE, KRB5_FILE, conf);
                    userGroupInformation = LoginUtil.login(USER_NAME, USER_KEYTAB_FILE, KRB5_FILE, conf);
                    log.info("[huaweiHdfsSourceDTO]完成登录，{}",userGroupInformation.getUserName());
                } catch (IOException e) {
                    throw new DtLoaderException("login kerberos failed", e);
                }
            }

            try {
                return userGroupInformation;
            } catch (Exception var6) {
                throw new DtLoaderException("login kerberos failed", var6);
            }
        }
    }


    /**
     * Get user realm process
     */
    public static String getUserRealm() {
        String serverRealm = System.getProperty("SERVER_REALM");
        if (serverRealm != null && serverRealm != "") {
            AUTH_HOST_NAME = "hadoop." + serverRealm.toLowerCase();
        } else {
            serverRealm = getKrb5DomainRealm();
            if (serverRealm != null && serverRealm != "") {
                AUTH_HOST_NAME = "hadoop." + serverRealm.toLowerCase();
            } else {
                AUTH_HOST_NAME = "hadoop";
            }
        }
        return AUTH_HOST_NAME;
    }


    /**
     * JAVA_VENDER
     */
    public static final String JAVA_VENDER = "java.vendor";
    /**
     * IBM_FLAG
     */
    public static final String IBM_FLAG = "IBM";
    /**
     * CONFIG_CLASS_FOR_IBM
     */
    public static final String CONFIG_CLASS_FOR_IBM = "com.ibm.security.krb5.internal.Config";
    /**
     * CONFIG_CLASS_FOR_SUN
     */
    public static final String CONFIG_CLASS_FOR_SUN = "sun.security.krb5.Config";
    /**
     * METHOD_GET_INSTANCE
     */
    public static final String METHOD_GET_INSTANCE = "getInstance";
    /**
     * METHOD_GET_DEFAULT_REALM
     */
    public static final String METHOD_GET_DEFAULT_REALM = "getDefaultRealm";
    /**
     * DEFAULT_REALM
     */
    public static final String DEFAULT_REALM = "HADOOP.COM";


    /**
     * Get Krb5 Domain Realm
     */
    public static String getKrb5DomainRealm() {
        Class<?> krb5ConfClass;
        String peerRealm = null;
        try {
            if (System.getProperty(JAVA_VENDER).contains(IBM_FLAG)) {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_IBM);
            } else {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_SUN);
            }

            Method getInstanceMethod = krb5ConfClass.getMethod(METHOD_GET_INSTANCE);
            Object kerbConf = getInstanceMethod.invoke(krb5ConfClass);

            Method getDefaultRealmMethod = krb5ConfClass.getDeclaredMethod(METHOD_GET_DEFAULT_REALM);
            if (getDefaultRealmMethod.invoke(kerbConf) instanceof String) {
                peerRealm = (String) getDefaultRealmMethod.invoke(kerbConf);
            }
            log.info("Get default realm successfully, the realm is : {}", peerRealm);

        } catch (ClassNotFoundException e) {
            peerRealm = DEFAULT_REALM;
            log.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            peerRealm = DEFAULT_REALM;
            log.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        }

        return peerRealm;
    }


    /**
     * 获取dbName
     * @param jdbcUrl
     * @return
     */
    public static String getDatabaseName(String jdbcUrl) {
        String dbName ="";

        // Patterns for various JDBC URL formats
        String[] patterns = {
                "/([^:/?;]+)(\\?|$)",                    // General pattern for dbName after '/'
                ";libraries=([^;?]+)",                   // AS400 pattern
                ";DatabaseName=([^;?]+)",                // SQL Server / Greenplum pattern (with possible ?)
                ":INFORMIXSERVER=[^:]+:([^:/?;]+)",      // Informix pattern
                "@//[^:]+:\\d+/([^:/?;]+)",              // Oracle thin pattern
                "/([^;?]+)[;?]?",                // 匹配 `/dbName`，可能跟有 `;` 或 `?`
                ":([a-zA-Z0-9_]+)(;|$)"          // 匹配 `:dbName`，后面可能是 `;` 或字符串的结尾
        };
        // Iterate over the patterns to find a match
        for (String pattern : patterns) {
            Matcher matcher = Pattern.compile(pattern).matcher(jdbcUrl);
            if (matcher.find()) {
                dbName = matcher.group(1);
                break;
            }
        }

        return dbName;
    }
}
