/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.db2;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.*;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 16:09 2020/1/7
 * @Description：Db2 客户端
 */
@Slf4j
public class Db2Client extends AbsRdbmsClient {
    private static final String TABLE_QUERY = "select tabname from syscat.tables where tabschema = '%s'";

    private static final String DATABASE_QUERY = "select schemaname from syscat.schemata where ownertype != 'S'";

    private static final String TABLE_BY_SCHEMA = "select TABLE_NAME AS Name from SYSIBM.TABLES where TABLE_SCHEMA='%s' %s";
    private static final String TABLE_IS_IN_SCHEMA = "select TABLE_NAME AS Name from SYSIBM.TABLES where TABLE_SCHEMA='%s' and TABLE_NAME = '%s' ";

    // 获取db2的当前database
    private static final String CURRENT_DB = "select current server from sysibm.sysdummy1";

    // 根据schema选表表名模糊查询
    private static final String SEARCH_SQL = " AND TABLE_NAME LIKE '%s' ";

    // 限制条数语句
    private static final String LIMIT_SQL = " fetch first  %s rows only ";

    // 获取当前版本号
    private static final String SHOW_VERSION = "SELECT SERVICE_LEVEL FROM SYSIBMADM.ENV_INST_INFO";


    // 获取指定schema下的表，包括视图
    private static final String SHOW_TABLE_AND_VIEW_BY_SCHEMA_SQL = "SELECT table_name FROM information_schema.tables WHERE table_schema = '%s' %s";


    //获取所有表名，包括视图，表名前拼接schema，并对schema和tableName进行增加双引号处理
    private static final String ALL_TABLE_AND_VIEW_SQL = "SELECT '\"'||table_schema||'\".\"'||table_name||'\"' AS schema_table FROM information_schema.tables WHERE 1 = 1 %s order by schema_table ";

    // 获取指定schema下的表，不包括视图
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = "SELECT table_name FROM information_schema.tables WHERE table_schema = '%s' AND table_type = 'BASE TABLE' %s";

    //获取所有表名，不包括视图，表名前拼接schema，并对schema和tableName进行增加双引号处理
    private static final String ALL_TABLE_SQL = "SELECT '\"'||table_schema||'\".\"'||table_name||'\"' AS schema_table FROM information_schema.tables WHERE table_type = 'BASE TABLE' %s order by schema_table ";


    //pg 表数据量查询
    private static final String DB2_TABLE_ROW = "select card AS TABLE_ROWS  from syscat.tables WHERE TABSCHEMA ='%s' AND tabname = '%s'";
    // 字符集
    private static final String DB2_CODESET = "SELECT VALUE FROM SYSIBMADM.DBCFG WHERE NAME = 'codeset'";


    private static final String DONT_EXIST = "doesn't exist";

    private static final String JDBC_URL = "jdbc:db2://%s:%s/%s";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };


    @Override
    protected ConnFactory getConnFactory() {
        return new Db2ConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.DB2;
    }

    @Override
    public List<String> getTableList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(db2SourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet rs = null;
        try {
            statement = db2SourceDTO.getConnection().createStatement();
            if (Objects.nonNull(queryDTO) && Objects.nonNull(queryDTO.getLimit())) {
                // 设置最大条数
                statement.setMaxRows(queryDTO.getLimit());
            }
            StringBuilder constr = new StringBuilder();
            if (Objects.nonNull(queryDTO) && StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
                constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
            }
            //大小写区分，不传schema默认获取所有表，并且表名签名拼接schema，格式："schema"."tableName"
            String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : db2SourceDTO.getSchema();
            String querySql;
            if (StringUtils.isNotBlank(schema)) {
                querySql = queryDTO.getView() ? String.format(SHOW_TABLE_AND_VIEW_BY_SCHEMA_SQL, schema, constr.toString()) : String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, constr.toString());
            } else {
                querySql = queryDTO.getView() ? String.format(ALL_TABLE_AND_VIEW_SQL, constr.toString()) : String.format(ALL_TABLE_SQL, constr.toString());
            }
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(querySql);
            List<String> tableList = new ArrayList<>();
            while (rs.next()) {
                tableList.add(rs.getString(1));
            }
            return tableList;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
        }
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) iSource;

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = db2SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format("select remarks from syscat.tables where tabname = '%s'"
                    , queryDTO.getTableName()));
            while (resultSet.next()) {
                return resultSet.getString("REMARKS");
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to get the information of table: %s. Please contact DBA to check the database and table information: %s",
                    queryDTO.getTableName(), e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) source;
        Connection connection = getCon(source);
        String sql = queryDTO.getSql();
        String schema = db2SourceDTO.getSchema();
        Db2Downloader db2Downloader = new Db2Downloader(connection, sql, schema);
        db2Downloader.configure();
        return db2Downloader;
    }

    @Override
    public String getShowDbSql() {
        return DATABASE_QUERY;
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(sourceDTO);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }

        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        return String.format(TABLE_BY_SCHEMA, schema, constr.toString());
    }

    /**
     * 处理db2 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("\"") || !tableName.endsWith("\"")) {
            tableName = String.format("\"%s\"", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("\"") || !schema.endsWith("\"")) {
            schema = String.format("\"%s\"", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }


    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        String character = getCharacterCollation(source, queryDTO);
        if (character != null) {
            return character;
        } else {
            return "";
        }
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) source;
        Integer clearStatus = beforeColumnQuery(db2SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = db2SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(DB2_CODESET, queryDTO.getSchema(), queryDTO.getTableName()));
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                return dbTableName;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        String character = getCharacterCollation(source, queryDTO);
        if (character != null) {
            return character;
        } else {
            return "";
        }
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(db2SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = db2SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(DB2_TABLE_ROW, queryDTO.getSchema(), queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = (long) resultSet.getFloat(1);
            }
            if (tableRow < 0L) {
                resultSet = statement.executeQuery(String.format("select COUNT(1) from %s.%s", queryDTO.getSchema(), queryDTO.getTableName()));
                while (resultSet.next()) {
                    tableRow = resultSet.getInt(1);
                    return tableRow;
                }
            } else {
                return tableRow;
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
        }
        return tableRow;
    }


    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        Db2SourceDTO db2SourceDTO = (Db2SourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;
        ResultSet resultSet = null;

        try {
            log.info("------------------开始getColumnMetaData------------------");
            db2SourceDTO.setConnection(getTransConn(db2SourceDTO.getConnection()));
            DatabaseMetaData metaData = db2SourceDTO.getConnection().getMetaData();
            log.info("------------------start------------------");

            String catalog = db2SourceDTO.getConnection().getCatalog();
            pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> pkList = new ArrayList<>();
            while (pkRs.next()) {
                pkList.add(pkRs.getString("COLUMN_NAME"));
            }

            log.info("------------------执行pkRs结束------------------");

            fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> fkList = new ArrayList<>();
            while (fkRs.next()) {
                fkList.add(fkRs.getString("PKCOLUMN_NAME"));
            }

            log.info("------------------执行fkRs结束------------------");

            //oracle视图和oracle表名为小写的时候不支持查询索引
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();
            /*if ((!(rdbmsSourceDTO instanceof OracleSourceDTO) ||
                    (Arrays.stream(queryDTO.getTableTypes()).noneMatch("VIEW"::equalsIgnoreCase))
                            && !queryDTO.getTableName().matches(".*[a-z].*"))) {
                log.info("------------------单独执行uniqueRs  start ------------------");
                uniqueRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), true, false);
                uniqueRs.getStatement().setMaxRows(1);
                log.info("------------------单独执行uniqueRs  end ------------------");
                while (uniqueRs.next()) {
                    uniqueList.add(uniqueRs.getString("COLUMN_NAME"));
                }
                allIndexRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), false, false);
                log.info("------------------单独执行allIndexRs------------------");
                while (allIndexRs.next()) {
                    DsIndexDTO dsIndexDTO = new DsIndexDTO();
                    dsIndexDTO.setColumnName(allIndexRs.getString("COLUMN_NAME"));
                    dsIndexDTO.setUnique(allIndexRs.getBoolean("NON_UNIQUE"));
                    dsIndexDTO.setType(allIndexRs.getShort("TYPE"));
                    allIndexList.add(dsIndexDTO);
                }
            }

            log.info("------------------执行oracle视图和oracle表名为小写的时候不支持查询索引------------------");*/

            rsColumn = metaData.getColumns(catalog, queryDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                String columnName = rsColumn.getString("COLUMN_NAME");
                columnMetaDTO.setKey(columnName);
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));

                //获取字段注释
                String format = String.format("select COLNAME ,REMARKS from SYSCAT.COLUMNS where TABSCHEMA ='%s' and tabname = '%s' AND colname='%s'", queryDTO.getSchema(), queryDTO.getTableName(), columnName);
                statement = db2SourceDTO.getConnection().createStatement();
                resultSet = statement.executeQuery(format);
                String remark = "";
                while (resultSet.next()) {
                    remark = resultSet.getString(2);
                }
                if (StringUtils.isNotEmpty(remark)) {
                    columnMetaDTO.setComment(remark);
                }

                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = db2SourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " + transferSchemaAndTableName(db2SourceDTO, queryDTO) + " where 1=2";

            rs = statement.executeQuery(queryColumnSql);

            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(db2SourceDTO, clearStatus));
            DBUtil.closeDBResources(resultSet, null, null);
        }
        return newColumns;
    }

    /**
     * rdbms数据预览
     *
     * @param iSource
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        List<List<Object>> previewList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return previewList;
        }
        Statement stmt = null;
        ResultSet rs = null;
        PreparedStatement preparedStatement = null;
        try {
            if (iSource instanceof ImpalaSourceDTO && queryDTO.getPageNum() != null) {
                String sql = String.format("SELECT * from %s ", transferSchemaAndTableName(iSource, queryDTO));
                String whereSql = dealWhereSql(rdbmsSourceDTO, sql, queryDTO);
                String impalaOrderByColumn = queryDTO.getImpalaOrderByColumn();
                if (StringUtils.isEmpty(impalaOrderByColumn)) {
                    throw new DtLoaderException("Impala数据源预览分页数据需要order by的字段");
                }
                sql = sql + whereSql + String.format(" order by `%s` limit ? offset ?", impalaOrderByColumn);

                preparedStatement = rdbmsSourceDTO.getConnection().prepareStatement(sql);
                Integer previewNum = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                preparedStatement.setInt(1, previewNum);
                preparedStatement.setInt(2, (pageNum - 1) * previewNum);
                rs = preparedStatement.executeQuery();
            } else {
                stmt = rdbmsSourceDTO.getConnection().createStatement();
                //查询sql，默认预览100条
                String querySql = dealSql(rdbmsSourceDTO, queryDTO);
                String whereSql = dealWhereSql(rdbmsSourceDTO, querySql, queryDTO);
                querySql = querySql + whereSql;
                if (queryDTO.getPreviewNum() != null) {
                    int endIndex = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        endIndex = pageNum * endIndex;
                    }
                    stmt.setMaxRows(endIndex);
                }
                rs = stmt.executeQuery(querySql);

                if (queryDTO.getPreviewNum() != null) {
                    int previewNum = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        // 查询分页数据
                        int beginIndex = (pageNum - 1) * previewNum;
                        if (beginIndex > 0) {
                            // 设置读取数据的起始位置
                            rs.absolute(beginIndex);
                        }
                    }
                }
            }

            ResultSetMetaData rsmd = rs.getMetaData();
            //存储字段信息
            List<Object> metaDataList = Lists.newArrayList();
            //字段数量
            int len = rsmd.getColumnCount();
            for (int i = 0; i < len; i++) {
                metaDataList.add(rsmd.getColumnLabel(i + 1));
            }
            while (rs.next()) {
                //一个columnData存储一行数据信息
                ArrayList<Object> columnData = Lists.newArrayList();
                for (int i = 0; i < len; i++) {
                    String result = dealPreviewResult(rs.getObject(i + 1));
                    columnData.add(result);
                }
                previewList.add(columnData);
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return previewList;
    }

    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IS_IN_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }

}
