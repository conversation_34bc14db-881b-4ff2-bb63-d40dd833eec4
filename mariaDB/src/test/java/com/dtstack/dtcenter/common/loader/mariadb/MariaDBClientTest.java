package com.dtstack.dtcenter.common.loader.mariadb;

import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.source.MariaDBSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;

import java.sql.Connection;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @create 2022-08-29-16:48
 * @description:
 */
class MariaDBClientTest {

    public static void main(String[] args) throws Exception {
        getMariaDBConnection();
//        testMysqlConnection();
    }

    public static void getMariaDBConnection() throws Exception {
        ClientCache.setUserDir("E:\\IdeaProjects\\DatasourceX\\core\\pluginLibs");
        IClient client = ClientCache.getClient(DataSourceType.MariaDB.getVal());
        MariaDBSourceDTO source = MariaDBSourceDTO.builder()
                .url("jdbc:mariadb://*************")
                .username("root")
                .password("Cdyanfa_123456")
                .poolConfig(PoolConfig.builder().build())
                .build();
        Boolean conn = client.testCon(source);
    }

    public static void testMysqlConnection() throws Exception {
        IClient client = ClientCache.getClient(DataSourceType.MySQL.getVal());
        Mysql5SourceDTO source = Mysql5SourceDTO.builder()
                .url("**********************")
                .username("root")
                .password("root")
                .poolConfig(PoolConfig.builder().build())
                .build();
        Boolean isConnect = client.testCon(source);
    }

}
