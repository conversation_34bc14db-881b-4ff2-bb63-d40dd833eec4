package com.dtstack.dtcenter.common.loader.mariadb;

import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.source.DataBaseType;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2022-08-16-17:24
 * @description:
 */
public class MariaDBConnFactory extends ConnFactory {

    public MariaDBConnFactory() {
        driverName = DataBaseType.MariaDB.getDriverClassName();
        this.errorPattern = new MariaDBErrorPattern();
    }

    @Override
    protected String dealSourceUrl(RdbmsSourceDTO rdbmsSourceDTO) {
        String schema = rdbmsSourceDTO.getSchema();
        String url = rdbmsSourceDTO.getUrl();
        if (StringUtils.isNotEmpty(schema)){
            String[] urlAyy = url.split("/");
            if (urlAyy.length > 2){
                url = urlAyy[0] + "//" + urlAyy[2] + "/" +schema;
            }
        }
        return url;
    }

}
