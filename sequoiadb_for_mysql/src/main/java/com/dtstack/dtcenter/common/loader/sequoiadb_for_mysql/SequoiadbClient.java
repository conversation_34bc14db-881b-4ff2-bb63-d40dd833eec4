/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.sequoiadb_for_mysql;

import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.ReflectUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.*;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:18 2020/1/3
 * @Description：Mysql 客户端
 */
@Slf4j
public class SequoiadbClient extends AbsRdbmsClient {
    private static final String TABLE_NAME = "table_name";
    private static final String TABLE_TYPE = "table_type";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select database()";

    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "show databases like '%s'";

    private static final String DONT_EXIST = "doesn't exist";

    // 获取指定数据库下的表
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = "select table_name from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 获取指定数据库下的表（结果包含字段类型）
    private static final String SHOW_TABLE_TYPE_BY_SCHEMA_SQL = "select table_name,table_type from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 视图
    private static final String VIEW = "'VIEW'";

    /**
     * table count sql
     */
    private static final String MYSQL_TABLE_ROW = "SELECT TABLE_ROWS FROM information_schema.`TABLES` where TABLE_SCHEMA = '%s' and TABLE_NAME = '%s'";

    // 普通表
    private static final String BASE_TABLE = "'BASE TABLE'";

    // 表名正则匹配模糊查询
    private static final String SEARCH_SQL = " AND table_name LIKE '%s' ";

    // 限制条数语句
    private static final String LIMIT_SQL = " limit %s ";

    // 创建数据库
    private static final String CREATE_SCHEMA_SQL_TMPL = "create schema %s ";

    // 判断table是否在schema中
    private static final String TABLE_IS_IN_SCHEMA = "select table_name from information_schema.tables where table_schema='%s' and table_name = '%s'";

    // 获取当前版本号
    private static final String SHOW_VERSION = "select version()";

    @Override
    protected ConnFactory getConnFactory() {
        return new SequoiadbConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.MySQL;
    }

    @Override
    protected String transferTableName(String tableName) {
        return tableName.contains("`") ? tableName : String.format("`%s`", tableName);
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return getTableListBySchema(iSource, queryDTO);
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(iSource);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }
        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        List<String> tableType = Lists.newArrayList(BASE_TABLE);
        // 获取视图
        if (BooleanUtils.isTrue(queryDTO.getView())) {
            tableType.add(VIEW);
        }
        List<TableViewDTO> tableViewList = new ArrayList<>();
        String sql = String.format(SHOW_TABLE_TYPE_BY_SCHEMA_SQL, schema, String.join(",", tableType), constr.toString());
        Integer fetchSize = ReflectUtil.fieldExists(SqlQueryDTO.class, "fetchSize") ? queryDTO.getFetchSize() : null;
        List<Map<String, Object>> tableList = queryCustomColumn(iSource, fetchSize, sql, "get table exception according to schema...");
        for (Map<String, Object> table : tableList) {
            if (MapUtils.isNotEmpty(table)) {
                TableViewDTO tableViewDTO = new TableViewDTO();
                for (Map.Entry<String, Object> entry : table.entrySet()) {
                    String key = entry.getKey();
                    String value = String.valueOf(entry.getValue());
                    if (TABLE_TYPE.equalsIgnoreCase(key)) {
                        tableViewDTO.setType("BASE TABLE".equalsIgnoreCase(value) ? "TABLE" : value.toUpperCase());
                    }
                    if (TABLE_NAME.equalsIgnoreCase(key)) {
                        tableViewDTO.setName(value);
                    }
                }
                tableViewList.add(tableViewDTO);
            }
        }
        return tableViewList;
    }

    @Override
    public String getCharacterSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        String character = getCharacterCollation(iSource, queryDTO);
        if (character != null && character.indexOf("_") > 0) {
            String[] s = character.split("_");
            return s[0];
        } else {
            return "";
        }
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(mysql5SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            String currentDatabase = getCurrentDatabase(iSource);
            resultSet = statement.executeQuery(String.format(MYSQL_TABLE_ROW, currentDatabase, queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return tableRow;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) source;
        Integer clearStatus = beforeColumnQuery(mysql5SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        String currentDatabase = getCurrentDatabase(source);
        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format("SHOW TABLE STATUS FROM %s WHERE NAME = '%s'", currentDatabase, queryDTO.getTableName()));
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);

                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    String character = resultSet.getString(DtClassConsistent.PublicConsistent.collation);
                    return character;
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    protected String doDealType(ResultSetMetaData rsMetaData, Integer los) throws SQLException {
        int columnType = rsMetaData.getColumnType(los + 1);
        // text,mediumtext,longtext的jdbc类型名都是varchar，需要区分。不同的编码下，最大存储长度也不同。考虑1，2，3，4字节的编码

        if (columnType != Types.LONGVARCHAR) {
            return super.doDealType(rsMetaData, los);
        }

        int precision = rsMetaData.getPrecision(los + 1);
        if (precision >= 16383 && precision <= 65535) {
            return "TEXT";
        }

        if (precision >= 4194303 && precision <= 16777215) {
            return "MEDIUMTEXT";
        }

        if (precision >= 536870911 && precision <= 2147483647) {
            return "LONGTEXT";
        }

        return super.doDealType(rsMetaData, los);
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(mysql5SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery("show table status");
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);

                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    return resultSet.getString(DtClassConsistent.PublicConsistent.COMMENT);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) source;
        SequoiadbDownloader sequoiadbDownloader = new SequoiadbDownloader(getCon(source), queryDTO.getSql(), mysql5SourceDTO.getSchema());
        sequoiadbDownloader.configure();
        return sequoiadbDownloader;
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql =
                    "show full columns from " + transferSchemaAndTableName(sourceDTO, queryDTO);
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString("Field");
                String columnComment = rs.getString("Comment");
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the comment information of the field of the table: %s. Please contact the DBA to check the database and table information.",
                        queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }


    /**
     * 获取指定schema下的表，如果没有填schema，默认使用当前schema。支持正则匹配查询、条数限制
     *
     * @param sourceDTO 数据源信息
     * @param queryDTO  查询条件
     * @return
     */
    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(sourceDTO);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }

        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        List<String> tableType = Lists.newArrayList(BASE_TABLE);
        // 获取视图
        if (BooleanUtils.isTrue(queryDTO.getView())) {
            tableType.add(VIEW);
        }
        return String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, String.join(",", tableType), constr.toString());
    }

    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('`', '`');
    }
}
