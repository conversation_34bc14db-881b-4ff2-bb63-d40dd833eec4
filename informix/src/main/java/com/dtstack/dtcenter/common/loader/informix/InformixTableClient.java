/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.informix;

import com.dtstack.dtcenter.common.loader.rdbms.AbsTableClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * oracle表操作相关接口
 *
 * <AUTHOR>
 * date：Created in 10:57 上午 2020/12/3
 * company: www.dtstack.com
 */
@Slf4j
public class InformixTableClient extends AbsTableClient {

    // 获取表占用存储sql
    //新建的表查出来是空，所以需要重新聚合查询下
    private static final String TABLE_SIZE_SQL = "select sum(pagesize * nptotal)\n" +
            "from sysmaster:sysptnhdr\n" +
            "where partnum in\n" +
            "( select partnum from systables\n" +
            "  where tabname = '%s'\n" +
            "  union\n" +
            "  select partn from sysfragments f, systables t\n" +
            "  where f.tabid = t.tabid\n" +
            "  and t.tabname = '%s' )";

    private static final String ADD_COLUMN_SQL = "ALTER TABLE %s ADD COLUMN %s %s";

    private static final String ADD_COLUMN_COMMENT_SQL = "COMMENT ON COLUMN %s IS '%s'";

    @Override
    protected ConnFactory getConnFactory() {
        return new InformixConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.Informix;
    }

    @Override
    public List<String> showPartitions(ISourceDTO source, String tableName) {
        throw new DtLoaderException("The data source does not support get partition operation！");
    }

    @Override
    protected String getDropTableSql(String tableName) {
        return String.format("drop table if exists %s", tableName);
    }

    @Override
    protected String getTableSizeSql(String schema, String tableName) {
        return String.format(TABLE_SIZE_SQL, tableName, tableName);
    }

}
