/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.sqlserver2017;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Sqlserver2017SourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:30 2020/1/7
 * @Description：SqlServer 客户端
 */
@Slf4j
public class SqlServerClient extends AbsRdbmsClient {
    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "select * from master.dbo.sysdatabases where name like '%s'";

    private static final String TABLE_QUERY_ALL = "select * from sys.objects o JOIN sys.schemas s ON o.schema_id = s.schema_id where type='U' or type='V'";
    private static final String TABLE_QUERY = "select * from sys.objects o JOIN sys.schemas s ON o.schema_id = s.schema_id where type='U'";
    private static final String GET_SQLSERVER_CHARACTER_INFO = " SELECT collation_name  FROM sys.databases where name = '%s' ";

    private static final String SQL_SERVER_TABLE_ROW = "SELECT p.rows AS [Row Count]  FROM   sys.partitions p INNER JOIN sys.indexes i ON p.object_id = i.object_id AND p.index_id = i.index_id\n" +
            "WHERE  Object_schema_name(p.object_id) = '%s' AND Object_name(p.object_id) = '%s'";
    private static String SQL_SERVER_COLUMN_NAME = "column_name";
    private static String SQL_SERVER_COLUMN_COMMENT = "column_description";
    private static final String SCHEMAS_QUERY = "select distinct(sys.schemas.name) as schema_name from sys.objects,sys.schemas where sys.objects.type='U' and sys.objects.schema_id=sys.schemas.schema_id";
    private static final String COMMENT_QUERY = "SELECT B.name AS column_name, C.value AS column_description FROM sys.tables A INNER JOIN sys.columns B ON B.object_id = A.object_id LEFT JOIN sys.extended_properties C ON C.major_id = B.object_id AND C.minor_id = B.column_id WHERE A.name = N";
    // 获取正在使用数据库
    private static final String CURRENT_DB = "Select Name From Master..SysDataBases Where DbId=(Select Dbid From Master..SysProcesses Where Spid = @@spid)";
    /**
     * 根据schema获取对应的表：开启cdc的表
     */
    private static final String TABLE_BY_SCHEMA = "SELECT sys.tables.name AS table_name,sys.schemas.name AS schema_name \n" +
            "FROM sys.tables LEFT JOIN sys.schemas ON sys.tables.schema_id=sys.schemas.schema_id \n" +
            "WHERE sys.tables.type='U' AND sys.tables.is_tracked_by_cdc =1\n" +
            "AND sys.schemas.name = '%s'";

    // 获取当前版本号
    private static final String SHOW_VERSION = "SELECT @@VERSION";

    private static final String JDBC_URL = "**************************************";

    private static final String DONT_EXIST = "doesn't exist";
    private static final String SCHEMA_SQL = " and s.name='%s'";

    private static final String TABLE_IN_SCHEMA = "select table_name from INFORMATION_SCHEMA.TABLES where  table_schema='%s' and table_name ='%s'";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };

    @Override
    protected ConnFactory getConnFactory() {
        return new SQLServerConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.SQLSERVER_2017_LATER;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeQuery(sqlserver2017SourceDTO, queryDTO, false);

        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            String sql = queryDTO.getView() ? TABLE_QUERY_ALL : TABLE_QUERY;
            // 查询schema下的
            if (StringUtils.isNotBlank(queryDTO.getSchema())) {
                sql += String.format(SCHEMA_SQL, queryDTO.getSchema());
            }
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            int columnSize = rs.getMetaData().getColumnCount();
            while (rs.next()) {
                tableList.add(rs.getString(1));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return tableList;
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) source;
        Integer clearStatus = beforeQuery(sqlserver2017SourceDTO, queryDTO, false);
        String currentDatabase = getCurrentDatabase(source);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(GET_SQLSERVER_CHARACTER_INFO, currentDatabase));
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取sqlSever系统参数异常，%s",
                    e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserver2017SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SQL_SERVER_TABLE_ROW, queryDTO.getSchema(), queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return tableRow;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getCharacterSet(source, queryDTO);
    }

    @Override
    public String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getCharacterSet(source, queryDTO);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserver2017SourceDTO, queryDTO);

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(
                    "select c.name, cast(isnull(f.[value], '') as nvarchar(100)) as REMARKS\n" +
                            "from sys.objects c " +
                            "left join sys.extended_properties f on f.major_id = c.object_id and f.minor_id = 0 and f.class = 1\n" +
                            "where c.type = 'u'");
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    return resultSet.getString(DtClassConsistent.PublicConsistent.REMARKS);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;

        try {
            log.info("------------------开始getColumnMetaData------------------");
            rdbmsSourceDTO.setConnection(getTransConn(rdbmsSourceDTO.getConnection()));
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();
            log.info("------------------start------------------");

            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if(StringUtils.isNotEmpty(queryDTO.getDbName())){
                catalog=queryDTO.getDbName();
            }
            pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> pkList = new ArrayList<>();
            while (pkRs.next()) {
                pkList.add(pkRs.getString("COLUMN_NAME"));
            }

            log.info("------------------执行pkRs结束------------------");

            fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> fkList = new ArrayList<>();
            while (fkRs.next()) {
                fkList.add(fkRs.getString("PKCOLUMN_NAME"));
            }

            log.info("------------------执行fkRs结束------------------");

            //oracle视图和oracle表名为小写的时候不支持查询索引
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();
            /*if ((!(rdbmsSourceDTO instanceof OracleSourceDTO) ||
                    (Arrays.stream(queryDTO.getTableTypes()).noneMatch("VIEW"::equalsIgnoreCase))
                            && !queryDTO.getTableName().matches(".*[a-z].*"))) {
                log.info("------------------单独执行uniqueRs  start ------------------");
                uniqueRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), true, false);
                uniqueRs.getStatement().setMaxRows(1);
                log.info("------------------单独执行uniqueRs  end ------------------");
                while (uniqueRs.next()) {
                    uniqueList.add(uniqueRs.getString("COLUMN_NAME"));
                }
                allIndexRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), false, false);
                log.info("------------------单独执行allIndexRs------------------");
                while (allIndexRs.next()) {
                    DsIndexDTO dsIndexDTO = new DsIndexDTO();
                    dsIndexDTO.setColumnName(allIndexRs.getString("COLUMN_NAME"));
                    dsIndexDTO.setUnique(allIndexRs.getBoolean("NON_UNIQUE"));
                    dsIndexDTO.setType(allIndexRs.getShort("TYPE"));
                    allIndexList.add(dsIndexDTO);
                }
            }

            log.info("------------------执行oracle视图和oracle表名为小写的时候不支持查询索引------------------");*/

            rsColumn = metaData.getColumns(catalog, queryDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                columnMetaDTO.setKey(rsColumn.getString("COLUMN_NAME"));
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
                columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " +catalog+"." + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";

            rs = statement.executeQuery(queryColumnSql);

            log.info("------------------queryColumnSql{}------------------",queryColumnSql);
            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }
            Map<String, String> columnComments = getColumnComments((RdbmsSourceDTO) iSource, queryDTO);
            for (ColumnMetaDTO columnMetaDatum : newColumns) {
                columnMetaDatum.setComment(columnComments.get(columnMetaDatum.getKey()));
            }

        } catch (SQLException e) {
            log.error("------------------表{}，字段采集报错{}------------------",queryDTO.getTableName(),e.getMessage());
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) source;
        SqlServerDownloader sqlServerDownloader = new SqlServerDownloader(getCon(sqlserver2017SourceDTO), queryDTO.getSql(), sqlserver2017SourceDTO.getSchema());
        sqlServerDownloader.configure();
        return sqlServerDownloader;
    }

    @Override
    public String dealSql(ISourceDTO source, SqlQueryDTO sqlQueryDTO) {
        return "select top " + sqlQueryDTO.getPreviewNum() + " * from " + transferSchemaAndTableName(source, sqlQueryDTO);
    }

    @Override
    protected String getDbSeparator() {
        return "\"";
    }

    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (StringUtils.isBlank(schema)) {
            return transferTableName(tableName);
        }
        if (!tableName.startsWith("[") || !tableName.endsWith("]")) {
            tableName = String.format("[%s]", tableName);
        }
        if (!schema.startsWith("[") || !schema.endsWith("]")) {
            schema = String.format("[%s]", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String transferTableName(String tableName) {
        //如果传过来是[tableName]格式直接当成表名
        if (tableName.startsWith("[") && tableName.endsWith("]")) {
            return tableName;
        }
        //如果不是上述格式，判断有没有"."符号，有的话，第一个"."之前的当成schema，后面的当成表名进行[tableName]处理
        if (tableName.contains(".")) {
            //切割，表名中可能会有包含"."的情况，所以限制切割后长度为2
            String[] tables = tableName.split("\\.", 2);
            tableName = tables[1];
            return String.format("%s.%s", tables[0], tableName.contains("[") ? tableName : String.format("[%s]",
                    tableName));
        }
        //判断表名
        return String.format("[%s]", tableName);
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return String.format(TABLE_BY_SCHEMA, queryDTO.getSchema());
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        String result = null;
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        Integer clearStatus = beforeQuery(rdbmsSourceDTO, queryDTO, false);
        try (Connection connection = rdbmsSourceDTO.getConnection()){
            String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
            result = SqlServerDDLGenerator.generateTableDDL(connection, schema, queryDTO.getTableName());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return result;
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql = COMMENT_QUERY + addSingleQuotes(queryDTO.getTableName());
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString(SQL_SERVER_COLUMN_NAME);
                String columnComment = rs.getString(SQL_SERVER_COLUMN_COMMENT);
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            //获取表字段注释失败
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    private static String addSingleQuotes(String str) {
        str = str.contains("'") ? str : String.format("'%s'", str);
        return str;
    }

    @Override
    public String getShowDbSql() {
        return SCHEMAS_QUERY;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    public String getVersionSql() {
        return SHOW_VERSION;
    }
    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IN_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }
}
