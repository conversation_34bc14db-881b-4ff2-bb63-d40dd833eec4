/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.argodb;

import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.enums.StoredType;
import com.dtstack.dtcenter.common.loader.common.utils.*;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosConfigUtil;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosLoginUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.Table;
import com.dtstack.dtcenter.loader.dto.source.ArgodbSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.enums.ConnectionClearStatus;
import com.dtstack.dtcenter.loader.enums.HiveDBDataType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hive.common.type.HiveDate;
import org.apache.hadoop.hive.conf.HiveConf;
import org.apache.hadoop.hive.metastore.HiveMetaStoreClient;

import java.security.PrivilegedAction;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * argodb client
 *
 * <AUTHOR>
 * date：Created in 下午2:19 2021/5/6
 * company: www.dtstack.com
 */
@Slf4j
public class ArgodbClient extends AbsRdbmsClient {

    // 创建库指定注释
    private static final String CREATE_DB_WITH_COMMENT = "create database if not exists %s comment '%s'";

    // 创建库
    private static final String CREATE_DB = "create database %s";

    // 模糊查询查询指定schema下的表
    private static final String TABLE_BY_SCHEMA_LIKE = "show tables in %s like '%s'";

    private static final String TABLE_BY_SCHEMA= "show tables in %s like '%s'";
    // 模糊查询database
    private static final String SHOW_DB_LIKE = "show databases like '%s'";

    private static final String SHOW_TABLE_SQL = "show tables %s";

    // 根据schema选表表名模糊查询
    private static final String SEARCH_SQL = " like '%s' ";

    // desc db info
    private static final String DESC_DB_INFO = "desc database %s";

    @Override
    protected ConnFactory getConnFactory() {
        return new ArgodbConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.ArgoDB;
    }

    // metaStore 地址 key
    private final static String META_STORE_URIS_KEY = "hive.metastore.uris";

    // 是否启用 kerberos 认证
    private final static String META_STORE_SASL_ENABLED = "hive.metastore.sasl.enabled";

    // metaStore 地址 principal 地址
    private final static String META_STORE_KERBEROS_PRINCIPAL = "hive.metastore.kerberos.principal";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select current_database()";

    @Override
    public List<String> getTableList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;
        StringBuilder constr = new StringBuilder();
        if (Objects.nonNull(queryDTO) && StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        // 获取表信息需要通过show tables 语句
        String sql = String.format(SHOW_TABLE_SQL, constr.toString());
        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            statement = argodbSourceDTO.getConnection().createStatement();
            int maxLimit = 0;
            if (Objects.nonNull(queryDTO) && Objects.nonNull(queryDTO.getLimit())) {
                // 设置最大条数
                maxLimit = queryDTO.getLimit();
            }
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            int columnSize = rs.getMetaData().getColumnCount();
            int cnt = 0;
            while (rs.next()) {
                if (maxLimit > 0 && cnt >= maxLimit) {
                    break;
                }
                ++cnt;
                tableList.add(rs.getString(columnSize == 1 ? 1 : 2));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(argodbSourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimit(tableList, queryDTO);
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;
        if (Objects.nonNull(queryDTO) && StringUtils.isNotBlank(queryDTO.getSchema())) {
            argodbSourceDTO.setSchema(queryDTO.getSchema());
        }
        return getTableList(argodbSourceDTO, queryDTO);
    }

    @Override
    public String getTableMetaComment(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;
        try {
            return getTableMetaComment(argodbSourceDTO.getConnection(), queryDTO.getTableName());
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(argodbSourceDTO, clearStatus));
        }
    }

    /**
     * 获取表注释信息
     *
     * @param conn      数据源连接
     * @param tableName 表名
     * @return 表注释
     */
    private String getTableMetaComment(Connection conn, String tableName) {
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = conn.createStatement();
            resultSet = statement.executeQuery(String.format(DtClassConsistent.HadoopConfConsistent.DESCRIBE_EXTENDED
                    , tableName));
            while (resultSet.next()) {
                String columnName = resultSet.getString(1);
                if (StringUtils.isNotEmpty(columnName) && columnName.toLowerCase().contains(DtClassConsistent.HadoopConfConsistent.TABLE_INFORMATION)) {
                    String string = resultSet.getString(2);
                    if (StringUtils.isNotEmpty(string) && string.contains(DtClassConsistent.HadoopConfConsistent.HIVE_COMMENT)) {
                        String[] split = string.split(DtClassConsistent.HadoopConfConsistent.HIVE_COMMENT);
                        if (split.length > 1) {
                            return split[1].split("[,}\n]")[0].trim();
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    tableName), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, null);
        }
        return "";
    }

    private List<ColumnMetaDTO> getColumnMetaData(Connection conn, String tableName, Boolean filterPartitionColumns, ISourceDTO source, SqlQueryDTO queryDTO) {
        List<ColumnMetaDTO> columnMetaDTOS = new ArrayList<>();
        Statement stmt = null;
        ResultSet resultSet = null;
        //获取schema
        String schema = queryDTO.getSchema();
        schema  = StringUtils.isBlank(schema) ? queryDTO.getDbName() : schema;
        //拼接库名和表名
        tableName = String.format("%s.%s", schema, tableName);
        try {
            stmt = conn.createStatement();
            resultSet = stmt.executeQuery(String.format(DtClassConsistent.HadoopConfConsistent.DESCRIBE_EXTENDED, tableName));
            while (resultSet.next()) {
                String dataType = resultSet.getString(DtClassConsistent.PublicConsistent.DATA_TYPE);
                String colName = resultSet.getString(DtClassConsistent.PublicConsistent.COL_NAME);
                if (StringUtils.isEmpty(dataType) || StringUtils.isBlank(colName)) {
                    break;
                }
                colName = colName.trim();
                ColumnMetaDTO metaDTO = new ColumnMetaDTO();
                metaDTO.setType(dataType.trim());
                metaDTO.setKey(colName);
                metaDTO.setComment(resultSet.getString(DtClassConsistent.PublicConsistent.COMMENT));

                if (colName.startsWith("#") || "Detailed Table Information".equals(colName)) {
                    break;
                }
                columnMetaDTOS.add(metaDTO);
            }

            DBUtil.closeDBResources(resultSet, null, null);
            resultSet = stmt.executeQuery(String.format(DtClassConsistent.HadoopConfConsistent.DESCRIBE_EXTENDED, tableName));
            boolean partBegin = false;
            while (resultSet.next()) {
                String colName = resultSet.getString(DtClassConsistent.PublicConsistent.COL_NAME).trim();

                if (colName.contains("# Partition Information")) {
                    partBegin = true;
                }

                if (colName.startsWith("#")) {
                    continue;
                }

                if ("Detailed Table Information".equals(colName)) {
                    break;
                }

                // 处理分区标志
                if (partBegin && !colName.contains("Partition Type")) {
                    Optional<ColumnMetaDTO> metaDTO =
                            columnMetaDTOS.stream().filter(meta -> colName.trim().equals(meta.getKey())).findFirst();
                    metaDTO.ifPresent(columnMetaDTO -> columnMetaDTO.setPart(true));
                } else if (colName.contains("Partition Type")) {
                    //分区字段结束
                    partBegin = false;
                }
            }
            List<ColumnMetaDTO> columnMetaDataNew = new ArrayList<>();
            for (ColumnMetaDTO columnMetaDatum : columnMetaDTOS) {
                queryDTO.setColumnType(columnMetaDatum.getType());
                ColumnMetaDTO dataType = getDataType(source, queryDTO);
                if (Objects.nonNull(dataType)) {
                    columnMetaDatum.setDataType(dataType.getDataType());
                }
                columnMetaDataNew.add(columnMetaDatum);

            }
            return columnMetaDataNew.stream().filter(column -> !filterPartitionColumns || !column.getPart()).collect(Collectors.toList());
        } catch (SQLException e) {
            throw new DtLoaderException(String.format("Failed to get meta information for the fields of table :%s. Please contact the DBA to check the database table information,%s", tableName, e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, stmt, null);
        }
    }


    @Override
    public ColumnMetaDTO getDataType(ISourceDTO source, SqlQueryDTO queryDTO) {
        ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
        try {
            String columnType = queryDTO.getColumnType().toUpperCase();
            if (columnType.contains(HiveDBDataType.VARCHAR.name())) {
                columnType = HiveDBDataType.VARCHAR.name();
            }
            HiveDBDataType dataType = HiveDBDataType.getDataType(columnType.toUpperCase());
            columnMetaDTO.setDataType(dataType.getDataType());
        } catch (Exception e) {
            return columnMetaDTO;
        }

        return columnMetaDTO;
    }


    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;
        try {
            return getColumnMetaData(argodbSourceDTO.getConnection(), queryDTO.getTableName(), queryDTO.getFilterPartitionColumns(), sourceDTO, queryDTO);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(argodbSourceDTO, clearStatus));
        }
    }

    @Override
    public Boolean testCon(ISourceDTO sourceDTO) {
        Future<Boolean> future = null;
        try {
            // 使用线程池的方式来控制连通超时
            Callable<Boolean> call = () -> testConnection(sourceDTO);
            future = executor.submit(call);
            // 如果在设定超时(以秒为单位)之内，还没得到连通性测试结果，则认为连通性测试连接超时，不继续阻塞
            return future.get(EnvUtil.getTestConnTimeout(), TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            throw new DtLoaderException(String.format("Test connection timeout,%s", e.getMessage()), e);
        } catch (Exception e) {
            if (e instanceof DtLoaderException) {
                throw new DtLoaderException(e.getMessage(), e);
            }
            if (e.getCause() != null && e.getCause() instanceof DtLoaderException) {
                throw new DtLoaderException(e.getCause().getMessage(), e);
            }
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(future)) {
                future.cancel(true);
            }
        }
    }

    private Boolean testConnection(ISourceDTO sourceDTO) {
        // 先校验数据源连接性
        Boolean testCon = super.testCon(sourceDTO);
        if (!testCon) {
            return Boolean.FALSE;
        }
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;
        if (StringUtils.isBlank(argodbSourceDTO.getDefaultFS())) {
            return Boolean.TRUE;
        }
        if (StringUtils.isNotBlank(argodbSourceDTO.getMetaStoreUris())) {
            // 检查 metaStore 连通性
            checkMetaStoreConnect(argodbSourceDTO.getMetaStoreUris(), argodbSourceDTO.getKerberosConfig());
        }
        return Boolean.TRUE;
    }

    /**
     * 检查 metaStore 连通性
     *
     * @param metaStoreUris  metaStore 地址
     * @param kerberosConfig kerberos 配置
     */
    private synchronized void checkMetaStoreConnect(String metaStoreUris, Map<String, Object> kerberosConfig) {
        HiveConf hiveConf = new HiveConf();
        hiveConf.set(META_STORE_URIS_KEY, metaStoreUris);
        // 重新设置 metaStore 地址
        ReflectUtil.setField(HiveMetaStoreClient.class, "metastoreUris", null, null);
        if (MapUtils.isNotEmpty(kerberosConfig)) {
            // metaStore kerberos 认证需要
            hiveConf.setBoolean(META_STORE_SASL_ENABLED, true);
            // 做两步兼容：先取 hive.metastore.kerberos.principal 的值，再取 principal，最后再取 keytab 中的第一个 principal
            String metaStorePrincipal = MapUtils.getString(kerberosConfig, META_STORE_KERBEROS_PRINCIPAL, MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL));
            if (StringUtils.isBlank(metaStorePrincipal)) {
                String keytabPath = MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL_FILE);
                metaStorePrincipal = KerberosConfigUtil.getPrincipals(keytabPath).get(0);
                if (StringUtils.isBlank(metaStorePrincipal)) {
                    throw new DtLoaderException("hive.metastore.kerberos.principal is not null...");
                }
            }
            log.info("hive.metastore.kerberos.principal:{}", metaStorePrincipal);
            hiveConf.set(META_STORE_KERBEROS_PRINCIPAL, metaStorePrincipal);
        }
        KerberosLoginUtil.loginWithUGI(kerberosConfig).doAs(
                (PrivilegedAction<Boolean>) () -> {
                    HiveMetaStoreClient client = null;
                    try {
                        client = new HiveMetaStoreClient(hiveConf);
                        client.getAllDatabases();
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("metastore connection failed.:%s", e.getMessage()));
                    } finally {
                        if (Objects.nonNull(client)) {
                            client.close();
                        }
                    }
                    return true;
                }
        );
    }

    /**
     * 处理hive分区信息和sql语句
     *
     * @param sqlQueryDTO 查询条件
     * @return 处理后的数据预览sql
     */
    @Override
    public String dealSql(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO) {
        Map<String, String> partitions = sqlQueryDTO.getPartitionColumns();
        StringBuilder partSql = new StringBuilder();
        //拼接分区信息
        if (MapUtils.isNotEmpty(partitions)) {
            boolean check = true;
            partSql.append(" where ");
            Set<String> set = partitions.keySet();
            for (String column : set) {
                if (check) {
                    partSql.append(column).append("=").append(partitions.get(column));
                    check = false;
                } else {
                    partSql.append(" and ").append(column).append("=").append(partitions.get(column));
                }
            }
        }
        return "select * from " + transferSchemaAndTableName(sqlQueryDTO.getSchema(), sqlQueryDTO.getTableName()) + partSql.toString() + limitSql(sqlQueryDTO.getPreviewNum());
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        List<ColumnMetaDTO> columnMetaDTOS = getColumnMetaData(sourceDTO, queryDTO);
        List<ColumnMetaDTO> partitionColumnMeta = new ArrayList<>();
        columnMetaDTOS.forEach(columnMetaDTO -> {
            if (columnMetaDTO.getPart()) {
                partitionColumnMeta.add(columnMetaDTO);
            }
        });
        return partitionColumnMeta;
    }

    @Override
    public Table getTable(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) sourceDTO;

        Table tableInfo = new Table();
        try {
            tableInfo.setName(queryDTO.getTableName());
            // 获取表注释
            tableInfo.setComment(getTableMetaComment(argodbSourceDTO.getConnection(), queryDTO.getTableName()));
            // 先获取全部字段，再过滤
            List<ColumnMetaDTO> columnMetaDTOS = getColumnMetaData(argodbSourceDTO.getConnection(), queryDTO.getTableName(), false, sourceDTO, queryDTO);
            // 分区字段不为空表示是分区表
            if (ReflectUtil.fieldExists(Table.class, "isPartitionTable")) {
                tableInfo.setIsPartitionTable(CollectionUtils.isNotEmpty(TableUtil.getPartitionColumns(columnMetaDTOS)));
            }
            tableInfo.setColumns(TableUtil.filterPartitionColumns(columnMetaDTOS, queryDTO.getFilterPartitionColumns()));
            // 获取表结构信息
            getTable(tableInfo, argodbSourceDTO, queryDTO.getTableName());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL executed exception, %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(argodbSourceDTO, clearStatus));
        }
        return tableInfo;
    }

    /**
     * 获取表结构信息
     *
     * @param tableInfo       表信息
     * @param argodbSourceDTO 连接信息
     * @param tableName       表名
     */
    private void getTable(Table tableInfo, ArgodbSourceDTO argodbSourceDTO, String tableName) {
        List<Map<String, Object>> result = executeQuery(argodbSourceDTO, SqlQueryDTO.builder().sql("desc formatted " + tableName).build(), ConnectionClearStatus.NORMAL.getValue());
        for (Map<String, Object> row : result) {
            // category和attribute不可为空
            if (StringUtils.isBlank(MapUtils.getString(row, "category")) || StringUtils.isBlank(MapUtils.getString(row, "attribute"))) {
                continue;
            }
            // 去空格处理
            String category = MapUtils.getString(row, "category").trim();
            String attribute = MapUtils.getString(row, "attribute").trim();

            if (StringUtils.containsIgnoreCase(category, "Location")) {
                tableInfo.setPath(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "Type")) {
                tableInfo.setExternalOrManaged(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "field.delim")) {
                tableInfo.setDelim(DelimiterUtil.charAtIgnoreEscape(attribute));
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "Owner")) {
                tableInfo.setOwner(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "CreateTime")) {
                tableInfo.setCreatedTime(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "LastAccess")) {
                tableInfo.setLastAccess(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "CreatedBy")) {
                tableInfo.setCreatedBy(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "Database")) {
                tableInfo.setDb(attribute);
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "transactional")) {
                if (StringUtils.containsIgnoreCase(attribute, "true")) {
                    tableInfo.setIsTransTable(true);
                }
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "Type")) {
                tableInfo.setIsView(StringUtils.containsIgnoreCase(attribute, "VIEW"));
                continue;
            }

            if (StringUtils.containsIgnoreCase(category, "InputFormat")) {
                for (StoredType hiveStoredType : StoredType.values()) {
                    if (StringUtils.containsIgnoreCase(attribute, hiveStoredType.getInputFormatClass())) {
                        tableInfo.setStoreType(hiveStoredType.getValue());
                    }
                }
            }
        }
        // text 未获取到分隔符情况下添加默认值
        if (StringUtils.equalsIgnoreCase(StoredType.TEXTFILE.getValue(), tableInfo.getStoreType()) && Objects.isNull(tableInfo.getDelim())) {
            tableInfo.setDelim(DtClassConsistent.HiveConsistent.DEFAULT_FIELD_DELIMIT);
        }
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return StringUtils.isBlank(comment) ? String.format(CREATE_DB, dbName) : String.format(CREATE_DB_WITH_COMMENT, dbName, comment);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name cannot be empty!");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }


    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {

        String sql = String.format(TABLE_BY_SCHEMA, queryDTO.getDbName(), queryDTO.getTableName());
        return sql;
    }
    @Override
    protected String getFuzzySign() {
        return "*";
    }

    @Override
    public String getDescDbSql(String dbName) {
        return String.format(DESC_DB_INFO, dbName);
    }

    @Override
    protected Object dealResult(Object result) {
        if (result instanceof HiveDate && result != null) {
            try {
                HiveDate hiveresult = (HiveDate) result;
                return hiveresult.toString();
            } catch (DtLoaderException e) {
                log.error("Hivedate format transform String exception", e);
            }
        }
        return result;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }


    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('`', '`');
    }

    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }
}
