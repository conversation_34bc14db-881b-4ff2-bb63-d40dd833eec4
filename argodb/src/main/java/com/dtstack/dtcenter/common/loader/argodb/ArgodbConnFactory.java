package com.dtstack.dtcenter.common.loader.argodb;

import com.dtstack.dtcenter.common.loader.common.utils.PropertiesUtil;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.source.ArgodbSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataBaseType;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/5/7 10:32
 */
public class ArgodbConnFactory extends ConnFactory {
    public ArgodbConnFactory() {
        driverName = DataBaseType.ArgoDB.getDriverClassName();
        errorPattern = new ArgodbErrorPattern();
    }

    @Override
    public Connection getConn(ISourceDTO iSource, String taskParams) throws Exception {
        init();
        ArgodbSourceDTO argodbSourceDTO = (ArgodbSourceDTO) iSource;
//        Connection connection = KerberosLoginUtil.loginWithUGI(argodbSourceDTO.getKerberosConfig()).doAs(
//                (PrivilegedAction<Connection>) () -> {
//                    try {
//                        DriverManager.setLoginTimeout(30);
//                        String urlWithoutSchema = ArgodbDriverUtil.removeSchema(argodbSourceDTO.getUrl());
//
//                        Properties properties = PropertiesUtil.convertToProp(argodbSourceDTO);
//                        return DriverManager.getConnection(urlWithoutSchema, properties);
//                    } catch (SQLException e) {
//                        // 对异常进行统一处理
//                        throw new DtLoaderException(errorAdapter.connAdapter(e.getMessage(), errorPattern), e);
//                    }
//                }
//        );
//        return ArgodbDriverUtil.setSchema(connection, argodbSourceDTO.getUrl(), argodbSourceDTO.getSchema());

        Connection connection;
        try {
            DriverManager.setLoginTimeout(30);
//            String urlWithoutSchema = ArgodbDriverUtil.removeSchema(argodbSourceDTO.getUrl());

            Properties properties = PropertiesUtil.convertToProp(argodbSourceDTO);
            connection = DriverManager.getConnection(argodbSourceDTO.getUrl(), properties);
        } catch (SQLException e) {
            // 对异常进行统一处理
            throw new DtLoaderException(errorAdapter.connAdapter(e.getMessage(), errorPattern), e);
        }
        return ArgodbDriverUtil.setSchema(connection, argodbSourceDTO.getUrl(), argodbSourceDTO.getSchema());
    }
}
