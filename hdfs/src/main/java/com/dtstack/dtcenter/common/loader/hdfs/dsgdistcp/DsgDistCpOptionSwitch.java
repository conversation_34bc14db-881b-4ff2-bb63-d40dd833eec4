package com.dtstack.dtcenter.common.loader.hdfs.dsgdistcp;

import org.apache.commons.cli.Option;
import org.apache.hadoop.conf.Configuration;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-15:24
 */
public enum DsgDistCpOptionSwitch {
    IGNORE_FAILURES("distcp.ignore.failures", new Option("i", false, "Ignore failures during copy")),
    PRESERVE_STATUS("distcp.preserve.status", new Option("p", true, "preserve status (rbugpcax)(replication, block-size, user, group, permission, checksum-type, ACL, XATTR).  If -p is specified with no <arg>, then preserves replication, block size, user, group, permission and checksum type.")),
    SYNC_FOLDERS("distcp.sync.folders", new Option("update", false, "Update target, copying only missingfiles or directories")),
    DELETE_MISSING("distcp.delete.missing.source", new Option("delete", false, "Delete from target, files missing in source")),
    SSL_CONF("distcp.keystore.resource", new Option("mapredSslConf", true, "Configuration for ssl config file, to use with hftps://")),
    MAX_MAPS("distcp.max.maps", new Option("m", true, "Max number of concurrent maps to use for copy")),
    SOURCE_FILE_LISTING("distcp.source.listing", new Option("f", true, "List of files that need to be copied")),
    ATOMIC_COMMIT("distcp.atomic.copy", new Option("atomic", false, "Commit all changes or none")),
    WORK_PATH("distcp.work.path", new Option("tmp", true, "Intermediate work path to be used for atomic commit")),
    LOG_PATH("distcp.log.path", new Option("log", true, "Folder on DFS where distcp execution logs are saved")),
    COPY_STRATEGY("distcp.copy.strategy", new Option("strategy", true, "Copy strategy to use. Default is dividing work based on file sizes")),
    SKIP_CRC("distcp.skip.crc", new Option("skipcrccheck", false, "Whether to skip CRC checks between source and target paths.")),
    OVERWRITE("distcp.copy.overwrite", new Option("overwrite", false, "Choose to overwrite target files unconditionally, even if they exist.")),
    APPEND("distcp.copy.append", new Option("append", false, "Reuse existing data in target files and append new data to them if possible")),
    BLOCKING("", new Option("async", false, "Should distcp execution be blocking")),
    FILE_LIMIT("", new Option("filelimit", true, "(Deprecated!) Limit number of files copied to <= n")),
    SIZE_LIMIT("", new Option("sizelimit", true, "(Deprecated!) Limit number of files copied to <= n bytes")),
    BANDWIDTH("distcp.map.bandwidth.mb", new Option("bandwidth", true, "Specify bandwidth per map in MB")),
    DIRECT_WRITE(DsgDistCpConstants.CONF_LABEL_DIRECT_WRITE,
      new Option("direct", false, "Write files directly to the"
                         + " target location, avoiding temporary file rename."));
    private final String confLabel;
    private final Option option;

    private DsgDistCpOptionSwitch(String confLabel, Option option) {
        this.confLabel = confLabel;
        this.option = option;
    }

    public String getConfigLabel() {
        return this.confLabel;
    }

    public Option getOption() {
        return this.option;
    }

    public String getSwitch() {
        return this.option.getOpt();
    }

    public String toString() {
        return super.name() + " {" + "confLabel='" + this.confLabel + '\'' + ", option=" + this.option + '}';
    }

    public static void addToConf(Configuration conf, DsgDistCpOptionSwitch option, String value) {
        conf.set(option.getConfigLabel(), value);
    }

    public static void addToConf(Configuration conf, DsgDistCpOptionSwitch option) {
        conf.set(option.getConfigLabel(), "true");
    }
}
