package com.dtstack.dtcenter.common.loader.dm;

import com.dtstack.dtcenter.common.loader.rdbms.AbsTableClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;

/**
 * oracle表操作相关接口
 *
 * <AUTHOR>
 * date：Created in 10:57 上午 2020/12/3
 * company: www.dtstack.com
 */
@Slf4j
public class DmTableClient extends AbsTableClient {

    private String TABLE_SIZE_SQL = "SELECT\n" +
            "    CASE WHEN t2.bytes IS NULL THEN 0 ELSE t2.bytes END AS table_size\n" +
            "FROM\n" +
            "    (SELECT COUNT(segment_name) num FROM user_segments WHERE segment_name = '%s') t1\n" +
            "LEFT JOIN\n" +
            "    (SELECT bytes, COUNT(segment_name) num FROM user_segments WHERE segment_name = '%s' GROUP BY bytes) t2\n" +
            "    ON t1.num = t2.num;";

    @Override
    protected ConnFactory getConnFactory() {
        return new DmConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.DMDB;
    }

    @Override
    protected String getTableSizeSql(String schema, String tableName) {
        return String.format(TABLE_SIZE_SQL, tableName, tableName);
    }

}
