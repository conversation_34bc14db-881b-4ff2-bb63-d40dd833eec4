/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.mysql5;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5HiveSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.enums.HiveDBDataType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:00 2020/2/27
 * @Description：MySQL 客户端
 */
@Slf4j
public class MysqlClient extends AbsRdbmsClient {
    private static final String TABLE_NAME = "table_name";
    private static final String TABLE_TYPE = "table_type";

    // 普通表
    private static final String BASE_TABLE = "'BASE TABLE'";

    // 视图
    private static final String VIEW = "'VIEW'";

    private static final String DONT_EXIST = "doesn't exist";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select database()";

    // 获取指定数据库下的表
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = "select table_name from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 获取指定数据库下的表（结果包含字段类型）
    private static final String SHOW_TABLE_TYPE_BY_SCHEMA_SQL = "select table_name,table_type from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 表名正则匹配模糊查询
    private static final String SEARCH_SQL = " AND table_name REGEXP '%s' ";

    // 限制条数语句
    private static final String LIMIT_SQL = " limit %s ";

    /**
     * table count sql
     */
    private static final String MYSQL_TABLE_ROW = "SELECT TABLE_ROWS FROM information_schema.`TABLES` where TABLE_SCHEMA = '%s' and TABLE_NAME = '%s'";

    // 创建数据库
    private static final String CREATE_SCHEMA_SQL_TMPL = "create schema %s ";

    // 获取当前版本号
    private static final String SHOW_VERSION = "select version()";
    private static final String JDBC_URL = "jdbc:mysql://%s:%s/%s";


    // 判断table是否在schema中
    private static final String TABLE_IS_IN_SCHEMA = "select table_name from information_schema.tables where table_schema='%s' and table_name = '%s'";


    @Override
    protected ConnFactory getConnFactory() {
        return new MysqlConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.MySQL5_HIVE;
    }

    @Override
    protected String transferTableName(String tableName) {
        return tableName.contains("`") ? tableName : String.format("`%s`", tableName);
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {

        List<String> list=  new ArrayList<>();
        Mysql5HiveSourceDTO mysql8SourceDTO = (Mysql5HiveSourceDTO) iSource;
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(iSource);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }
        }
        log.info("current used schema：{}", schema);
        Integer clearStatus = beforeColumnQuery(mysql8SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        //查询库里面得数据
        String sql = String.format("SELECT t.TBL_NAME  FROM TBLS t JOIN DBS d ON t.DB_ID = d.DB_ID WHERE d.NAME = '%s'", schema);

        try {
            statement = mysql8SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                list.add(dbTableName);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get AllTable: %s's information error. Please contact the DBA to check the database、table information.",
                    schema, e));
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql8SourceDTO, clearStatus));
        }

        return list;
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Mysql5HiveSourceDTO mysql8SourceDTO = (Mysql5HiveSourceDTO) iSource;
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(iSource);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }
        }
        log.info("current used schema：{}", schema);
        Integer clearStatus = beforeQuery(mysql8SourceDTO, queryDTO,false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<TableViewDTO> tableViewList = new ArrayList<>();
        //查询库里面得数据
        String sql = String.format("SELECT t.TBL_NAME  FROM TBLS t JOIN DBS d ON t.DB_ID = d.DB_ID WHERE d.NAME = '%s'", schema);

        try {
            statement = mysql8SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                TableViewDTO  table = new TableViewDTO();
                table.setName(dbTableName);
                table.setType("TABLE");
                tableViewList.add(table);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get AllTable: %s's information error. Please contact the DBA to check the database、table information.",
                    schema, e));
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql8SourceDTO, clearStatus));
        }


        return tableViewList;
    }

    @Override
    public String getCharacterSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return "";
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return null;
    }


    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return "";
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Mysql5HiveSourceDTO mysql8SourceDTO = (Mysql5HiveSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(mysql8SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            statement = mysql8SourceDTO.getConnection().createStatement();
            String sql=String.format("SELECT tp.param_value AS table_comment\n" +
                    "FROM TBLS t\n" +
                    "JOIN DBS d ON t.DB_ID = d.DB_ID\n" +
                    "JOIN TABLE_PARAMS tp ON t.TBL_ID = tp.TBL_ID\n" +
                    "WHERE d.NAME = '%s'\n" +
                    "  AND t.TBL_NAME = '%s'\n" +
                    "  AND tp.param_key = 'comment';\n",queryDTO.getSchema(),queryDTO.getTableName());
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                String comment = resultSet.getString(1);
                return comment;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql8SourceDTO, clearStatus));
        }
        return null;
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        Mysql5HiveSourceDTO mysql8SourceDTO = (Mysql5HiveSourceDTO) source;
        MysqlDownloader mysqlDownloader = new MysqlDownloader(getCon(source), queryDTO.getSql(), mysql8SourceDTO.getSchema());
        mysqlDownloader.configure();
        return mysqlDownloader;
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql =
                    "show full columns from " + transferSchemaAndTableName(sourceDTO, queryDTO);
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString("Field");
                String columnComment = rs.getString("Comment");
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the comment information of the field of the table: %s. Please contact the DBA to check the database and table information.",
                        queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    /**
     * 获取指定schema下的表，如果没有填schema，默认使用当前schema。支持正则匹配查询、条数限制
     *
     * @param sourceDTO 数据源信息
     * @param queryDTO  查询条件
     * @return
     */
    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(sourceDTO);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error!,%s", e.getMessage()), e);
            }

        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, queryDTO.getTableNamePattern()));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        List<String> tableType = Lists.newArrayList(BASE_TABLE);
        // 获取视图
        if (BooleanUtils.isTrue(queryDTO.getView())) {
            tableType.add(VIEW);
        }
        return String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, String.join(",", tableType), constr.toString());
    }

    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }


    @Override
    public String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        return "";
    }

    @Override
    public String getTimeZoneByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {

        return "";
    }
    /**
     * 获取数据源版本对应的jdbcUrl
     *
     * @return 获取版本对应的 sql
     */
    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }


    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Mysql5HiveSourceDTO mysql8SourceDTO = (Mysql5HiveSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(mysql8SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> columnMetaDTOS=new ArrayList<>();
        try {
            statement = mysql8SourceDTO.getConnection().createStatement();
            Pattern columnPattern = Pattern.compile("(\\w+)\\((\\d+)(?:,\\s*(\\d+))?\\)");
            String sql="SELECT  c.COLUMN_NAME,  c.TYPE_NAME,  c.COMMENT AS COLUMN_COMMENT,  CASE WHEN c.TYPE_NAME LIKE 'varchar%' THEN c.INTEGER_IDX \n" +
                    "        ELSE 255 END AS FIELD_LENGTH, CASE  WHEN c.TYPE_NAME LIKE 'decimal%' THEN c.INTEGER_IDX ELSE 0 END AS FIELD_PRECISION\n" +
                    "FROM TBLS t\n" +
                    "JOIN DBS d ON t.DB_ID = d.DB_ID\n" +
                    "JOIN SDS s ON t.SD_ID = s.SD_ID\n" +
                    "JOIN COLUMNS_V2 c ON s.CD_ID = c.CD_ID\n" +
                    "WHERE d.NAME = '"+queryDTO.getSchema()+"' AND t.TBL_NAME = '"+queryDTO.getTableName()+"'";
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                String name = resultSet.getString(1);
                String dataType = resultSet.getString(2);
                String comment = resultSet.getString(3);
                ColumnMetaDTO columnMetaDTO=new ColumnMetaDTO();
                columnMetaDTO.setKey(name);
                columnMetaDTO.setType(dataType);
                columnMetaDTO.setComment(comment);
                Matcher matcher = columnPattern.matcher(dataType);
                if (matcher.find()) {
                    String type = matcher.group(1);
                    String precision = matcher.group(2);
                    String scale = matcher.group(3);
                    if(StringUtils.isNotEmpty(type)){
                        columnMetaDTO.setType(type);
                    }
                    if(StringUtils.isNotEmpty(precision)){
                        columnMetaDTO.setPrecision(Integer.valueOf(precision));
                    }else{
                        columnMetaDTO.setPrecision(255);
                    }
                    if(StringUtils.isNotEmpty(scale)){
                        columnMetaDTO.setScale(Integer.valueOf(scale));
                    }else{
                        columnMetaDTO.setScale(0);
                    }
                }
                //datatype
                HiveDBDataType hiveDBDataType = HiveDBDataType.getDataType(columnMetaDTO.getType().toUpperCase());
                columnMetaDTO.setDataType(hiveDBDataType.getDataType());
                columnMetaDTO.setDateType(hiveDBDataType.getColumnDataType());
                columnMetaDTOS.add(columnMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql8SourceDTO, clearStatus));
        }

        return columnMetaDTOS;
    }

    @Override
    public ColumnMetaDTO getDataType(ISourceDTO source, SqlQueryDTO queryDTO) {
        ColumnMetaDTO columnMetaDTO=new ColumnMetaDTO();
        try{
            String columnType = queryDTO.getColumnType().toUpperCase();
            if(columnType.contains(HiveDBDataType.VARCHAR.name())){
                columnType=HiveDBDataType.VARCHAR.name();
            }
            HiveDBDataType dataType = HiveDBDataType.getDataType(columnType.toUpperCase());
            columnMetaDTO.setDataType(dataType.getDataType());
        }catch (Exception e){
            return columnMetaDTO;
        }

        return columnMetaDTO;
    }

}
