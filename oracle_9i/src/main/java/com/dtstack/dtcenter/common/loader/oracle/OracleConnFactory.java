/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oracle;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dtstack.dtcenter.common.loader.common.utils.JSONUtil;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataBaseType;
import oracle.jdbc.pool.OracleDataSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 12:01 2020/1/6
 * @Description：Oracle 连接工厂
 */
public class OracleConnFactory extends ConnFactory {
    public OracleConnFactory() {
        driverName = DataBaseType.Oracle.getDriverClassName();
        errorPattern = new OracleErrorPattern();
    }

    @Override
    public Connection getConn(ISourceDTO iSource, String taskParams) throws Exception {
        if (iSource == null) {
            throw new DtLoaderException(" source is null");
        }
        try {
            RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;

            /*if (StringUtils.isNotEmpty(rdbmsSourceDTO.getProperties())) {
                JSONObject propertiesJson = JSONUtil.parseJsonObject(rdbmsSourceDTO.getProperties());
                if (propertiesJson.containsKey("connectMode")) {
                    //连接方式：SID、服务名、TNS
                    String connectMode = propertiesJson.getString("connectMode");
                    if (connectMode.equalsIgnoreCase("TNS")) {
                        //TNS名称
                        String serviceName = propertiesJson.getString("serviceName");
                        //TNS ora文件
                        String oraFilePath = propertiesJson.getString("oraFilePath");
                        String url = "jdbc:oracle:thin:@" + serviceName;

                        //方式一：直接从tnsnames.ora文件中获取条目并将其附加到jdbc驱动程序字符串，如下所示
                        //String serviceName = "(DESCRIPTION = (ADDRESS_LIST=(ADDRESS = (PROTOCOL=tcp) (HOST=*************) (PORT=1521)))(CONNECT_DATA = (SERVICE_NAME = xe)))";
                        //String url = "jdbc:oracle:thin:@" + serviceName;

                        //方式二：在操作系统中设置环境变量，指定tns ora文件所在目录，如下所示
                        System.setProperty("oracle.net.tns_admin", oraFilePath);
                        rdbmsSourceDTO.setUrl(url);

                    } else if (connectMode.equalsIgnoreCase("SID") || connectMode.equalsIgnoreCase("SERVICE_NAME")) {
                        // jdbc url连接方式如：
                        // 服务名：***********************************************
                        // SID：****************************************************************************************************
                    }
                }
            }*/

            boolean isStart = rdbmsSourceDTO.getPoolConfig() != null;
            Connection connection = isStart && MapUtils.isEmpty(rdbmsSourceDTO.getKerberosConfig()) ?
                    getCpConn(rdbmsSourceDTO) : getSimpleConn(rdbmsSourceDTO);

            if (rdbmsSourceDTO.getSourceType().equals(DataSourceTypeEnum.GREENPLUM6.getVal())) {
                return connection;
            }
            return setSchema(connection, rdbmsSourceDTO.getSchema());
        } catch (Exception e) {
            // 对异常进行统一处理
            throw new DtLoaderException(errorAdapter.connAdapter(e.getMessage(), errorPattern), e);
        }
    }
}
