/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hbase;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosLoginUtil;
import com.dtstack.dtcenter.common.loader.hbase.pool.HbasePoolManager;
import com.dtstack.dtcenter.common.loader.hbase.util.DsgExportData;
import com.dtstack.dtcenter.common.loader.hbase.util.DsgExportSnapshot;
import com.dtstack.dtcenter.common.loader.hbase.util.DsgImportData;
import com.dtstack.dtcenter.loader.client.IHbase;
import com.dtstack.dtcenter.loader.dto.HbaseQueryDTO;
import com.dtstack.dtcenter.loader.dto.filter.TimestampFilter;
import com.dtstack.dtcenter.loader.dto.source.HbaseSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.protobuf.generated.HBaseProtos;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hdfs.HAUtil;
import org.apache.hadoop.mapreduce.Job;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.PrivilegedAction;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * hbase 新客户端，提供hbase特有的一些方法
 *
 * <AUTHOR>
 * date：Created in 10:23 上午 2020/12/2
 * company: www.dtstack.com
 */
@Slf4j
public class HbaseClientSpecial implements IHbase {

    // 数据预览最大条数
    private static final Integer MAX_PREVIEW_NUM = 5000;

    // 数据预览默认条数
    private static final Integer DEFAULT_PREVIEW_NUM = 100;

    // rowkey
    private static final String ROWKEY = "rowkey";

    // 列族:列名
    private static final String FAMILY_QUALIFIER = "%s:%s";

    // 列的时间戳
    private static final String TIMESTAMP = "timestamp";


    @Override
    public Boolean isDbExists(ISourceDTO source, String namespace) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Admin admin = null;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = connection.getAdmin();
            NamespaceDescriptor namespaceDescriptor = admin.getNamespaceDescriptor(namespace);
            if (Objects.nonNull(namespaceDescriptor)) {
                return true;
            }
        } catch (NamespaceNotFoundException namespaceNotFoundException) {
            log.error("namespace [{}] not found!", namespace);
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get namespace exception, namespace：'%s', %s", namespace, e.getMessage()), e);
        } finally {
            close(admin);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
        return false;
    }

    @Override
    public Boolean createHbaseTable(ISourceDTO source, String tbName, String[] colFamily) {
        return createHbaseTable(source, null, tbName, colFamily);
    }

    @Override
    public Boolean createHbaseTable(ISourceDTO source, String namespace, String tbName, String[] colFamily) {
        if (StringUtils.isNotBlank(namespace) && !tbName.contains(":")) {
            tbName = String.format("%s:%s", namespace, tbName);
        }
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        TableName tableName = TableName.valueOf(tbName);
        Connection connection = null;
        Admin admin = null;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = connection.getAdmin();
            if (admin.tableExists(tableName)) {
                throw new DtLoaderException(String.format("The current table already exists！:'%s'", tbName));
            } else {
                HTableDescriptor hTableDescriptor = new HTableDescriptor(tableName);
                for (String str : colFamily) {
                    HColumnDescriptor hColumnDescriptor = new HColumnDescriptor(str);
                    hTableDescriptor.addFamily(hColumnDescriptor);
                }
                admin.createTable(hTableDescriptor);
                log.info("hbase create table '{}' success!", tableName);
            }
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("hbase failed to create table！namespace：'%s'，table name：'%s'，column family：'%s'", namespace, tbName, Arrays.toString(colFamily)), e);
        } finally {
            close(admin);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
        return true;
    }

    @Override
    public Boolean deleteHbaseTable(ISourceDTO source, String tableName) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        TableName tbName = TableName.valueOf(tableName);
        Connection connection = null;
        Admin admin = null;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = connection.getAdmin();
            admin.disableTable(tbName);
            admin.deleteTable(tbName);
            log.info("delete hbase table success, table name {}", tbName);
        } catch (Exception e) {
            log.error("delete hbase table error, table name: {}", tbName, e);
            throw new DtLoaderException(String.format("hbase failed to delete table！table name: %s", tableName), e);
        } finally {
            close(admin);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
        return true;
    }

    @Override
    public Boolean deleteHbaseTable(ISourceDTO source, String namespace, String tableName) {
        if (StringUtils.isNotBlank(namespace) && !tableName.contains(":")) {
            tableName = String.format("%s:%s", namespace, tableName);
        }
        return deleteHbaseTable(source, tableName);
    }

    @Override
    public List<String> scanByRegex(ISourceDTO source, String tbName, String regex) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<String> results = Lists.newArrayList();
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            table = connection.getTable(TableName.valueOf(tbName));
            Scan scan = new Scan();
            org.apache.hadoop.hbase.filter.Filter rowFilter = new org.apache.hadoop.hbase.filter.RowFilter(CompareFilter.CompareOp.EQUAL, new RegexStringComparator(regex));
            scan.setFilter(rowFilter);
            rs = table.getScanner(scan);
            for (Result r : rs) {
                results.add(Bytes.toString(r.getRow()));
            }
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Hbase scans data abnormally according to regular！,regex：%s", regex), e);
        } finally {
            close(rs, table);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
        return results;
    }

    @Override
    public Boolean deleteByRowKey(ISourceDTO source, String tbName, String family, String qualifier, List<String> rowKeys) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        if (CollectionUtils.isEmpty(rowKeys)) {
            throw new DtLoaderException("The rowKey to be deleted cannot be empty！");
        }
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            table = connection.getTable(TableName.valueOf(tbName));
            for (String rowKey : rowKeys) {
                Delete delete = new Delete(Bytes.toBytes(rowKey));
                delete.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier));
                table.delete(delete);
                log.info("delete hbase rowKey success , rowKey {}", rowKey);
            }
            return true;
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("hbase delete data exception! rowKeys： %s,%s", rowKeys, e.getMessage()), e);
        } finally {
            close(table);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
    }

    @Override
    public Boolean putRow(ISourceDTO source, String tableName, String rowKey, String family, String qualifier, String data) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            table = connection.getTable(TableName.valueOf(tableName));
            Put put = new Put(Bytes.toBytes(rowKey));
            put.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier), Bytes.toBytes(data));
            table.put(put);
            return true;
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("hbase insert data exception! rowKey： %s， data： %s, error: %s", rowKey, data, e.getMessage()), e);
        } finally {
            close(table);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
    }

    @Override
    public String getRow(ISourceDTO source, String tableName, String rowKey, String family, String qualifier) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        String row = null;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            table = connection.getTable(TableName.valueOf(tableName));
            Get get = new Get(Bytes.toBytes(rowKey));
            get.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier));
            Result result = table.get(get);
            row = Bytes.toString(result.getValue(Bytes.toBytes(family), Bytes.toBytes(qualifier)));
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Hbase gets data exception! rowKey： %s , %s", rowKey, e.getMessage()), e);
        } finally {
            close(table);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
        return row;
    }

    @Override
    public List<List<String>> preview(ISourceDTO source, String tableName, Integer previewNum) {
        return preview(source, tableName, Maps.newHashMap(), previewNum);
    }

    @Override
    public List<List<String>> preview(ISourceDTO source, String tableName, List<String> familyList, Integer previewNum) {
        Map<String, List<String>> familyQualifierMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(familyList)) {
            familyList.forEach(family -> familyQualifierMap.put(family, null));
        }
        return preview(source, tableName, familyQualifierMap, previewNum);
    }

    @Override
    public List<List<String>> preview(ISourceDTO source, String tableName, Map<String, List<String>> familyQualifierMap, Integer previewNum) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<List<String>> previewList = Lists.newArrayList();
        try {
            // 获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            table = connection.getTable(TableName.valueOf(tableName));
            Scan scan = new Scan();
            // 计算数据预览条数，最大 5000，默认 100
            if (Objects.isNull(previewNum) || previewNum <= 0) {
                previewNum = DEFAULT_PREVIEW_NUM;
            } else if (previewNum > MAX_PREVIEW_NUM) {
                previewNum = MAX_PREVIEW_NUM;
            }
            // 支持添加列族、列名过滤
            if (MapUtils.isNotEmpty(familyQualifierMap)) {
                for (String family : familyQualifierMap.keySet()) {
                    List<String> qualifiers = familyQualifierMap.get(family);
                    if (CollectionUtils.isNotEmpty(qualifiers)) {
                        for (String qualifier : qualifiers) {
                            scan.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier));
                        }
                    } else {
                        scan.addFamily(Bytes.toBytes(family));
                    }
                }
            }
            // 数据预览限制返回条数
            scan.setMaxResultSize(previewNum);
            rs = table.getScanner(scan);
            List<Result> results = Lists.newArrayList();
            for (Result row : rs) {
                if (CollectionUtils.isEmpty(row.listCells())) {
                    continue;
                }
                results.add(row);
                if (results.size() >= previewNum) {
                    break;
                }
            }
            if (CollectionUtils.isEmpty(results)) {
                return previewList;
            }
            // 列名、值对应信息
            Map<String, List<String>> columnValueMap = Maps.newHashMap();
            // rowKey 集合
            List<String> rowKeyList = Lists.newArrayList();
            // timestamp 集合，取当前rowkey 最新字段更新的时间作为整个rowkey的timestamp
            List<String> timeStampList = Lists.newArrayList();
            columnValueMap.put(ROWKEY, rowKeyList);
            columnValueMap.put(TIMESTAMP, timeStampList);
            // 行号
            int rowNum = 0;
            for (Result result : results) {
                rowNum++;
                // 不设置获取版本，cells中默认只会返回最新版本的cell，不会存在多个版本并存的情况
                List<Cell> cells = result.listCells();
                long timestamp = 0L;
                String rowKey = null;
                for (Cell cell : cells) {
                    rowKey = Bytes.toString(cell.getRowArray(), cell.getRowOffset(), cell.getRowLength());
                    String family = Bytes.toString(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength());
                    String qualifier = Bytes.toString(cell.getQualifierArray(), cell.getQualifierOffset(), cell.getQualifierLength());
                    String column = String.format(FAMILY_QUALIFIER, family, qualifier);
                    List<String> columnList = columnValueMap.get(column);
                    // 如果为空则补全 null
                    if (Objects.isNull(columnList)) {
                        columnList = Lists.newArrayList();
                        for (int i = 0; i < rowNum - 1; i++) {
                            columnList.add(null);
                        }
                        columnValueMap.put(column, columnList);
                    }
                    String value = Bytes.toString(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
                    columnList.add(value);
                    //取到最新变动的时间
                    if (cell.getTimestamp() > timestamp) {
                        timestamp = cell.getTimestamp();
                    }
                }
                List<String> rowKeyColumnList = columnValueMap.get(ROWKEY);
                List<String> timestampColumnList = columnValueMap.get(TIMESTAMP);
                rowKeyColumnList.add(rowKey);
                timestampColumnList.add(String.valueOf(timestamp));
                int finalRowNum = rowNum;
                // 没有的字段补充null值
                columnValueMap.forEach((key, value) -> {
                    if (value.size() < finalRowNum) {
                        value.add(null);
                    }
                });
            }
            List<String> columnMetaDatas = new ArrayList<>(columnValueMap.keySet());
            columnMetaDatas.remove(ROWKEY);
            columnMetaDatas.remove(TIMESTAMP);
            // 排序，将rowKey放第一行，timestamp放最后一行
            Collections.sort(columnMetaDatas);
            columnMetaDatas.add(0, ROWKEY);
            columnMetaDatas.add(TIMESTAMP);
            previewList.add(columnMetaDatas);
            for (int i = 0; i < rowNum; i++) {
                List<String> row = Lists.newArrayList();
                for (String columnMetaData : columnMetaDatas) {
                    row.add(columnValueMap.get(columnMetaData).get(i));
                }
                previewList.add(row);
            }
            return previewList;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Data preview failed,%s", e.getMessage()), e);
        } finally {
            close(table, rs);
            closeConnection(connection, hbaseSourceDTO);
            HbaseClient.destroyProperty();
        }
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO source, HbaseQueryDTO hbaseQueryDTO, TimestampFilter timestampFilter) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<Result> results = Lists.newArrayList();
        List<Map<String, Object>> executeResult = Lists.newArrayList();
        try {
            // 获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            List<String> columns = hbaseQueryDTO.getColumns();
            // 转化表名为 hbase TableName
            TableName tableName = TableName.valueOf(hbaseQueryDTO.getTableName());
            table = connection.getTable(tableName);
            Scan scan = new Scan();
            // 指定 hbase 扫描列，格式 --> 列族:列名
            if (CollectionUtils.isNotEmpty(columns)) {
                for (String column : columns) {
                    String[] familyAndQualifier = column.split(":");
                    if (familyAndQualifier.length < 2) {
                        continue;
                    }
                    scan.addColumn(Bytes.toBytes(familyAndQualifier[0]), Bytes.toBytes(familyAndQualifier[1]));
                }
            }
            // 获取 common-loader 定义的自定义查询器并转化为 hbase 中的 filter
            com.dtstack.dtcenter.loader.dto.filter.Filter loaderFilter = hbaseQueryDTO.getFilter();

            boolean isAccurateQuery = false;
            if (Objects.nonNull(loaderFilter)) {
                if (loaderFilter instanceof com.dtstack.dtcenter.loader.dto.filter.FilterList) {
                    com.dtstack.dtcenter.loader.dto.filter.FilterList loaderFilterList = (com.dtstack.dtcenter.loader.dto.filter.FilterList) loaderFilter;
                    FilterList hbaseFilterList = new FilterList(convertOp(loaderFilterList.getOperator()));
                    convertFilter(loaderFilterList, hbaseFilterList);
                    for (Filter filter : hbaseFilterList.getFilters()) {
                        if (getAccurateQuery(table, results, filter)) {
                            isAccurateQuery = true;
                            break;
                        }
                    }
                    scan.setFilter(hbaseFilterList);
                } else {
                    Filter filter = FilterType.get(loaderFilter);
                    if (getAccurateQuery(table, results, filter)) {
                        isAccurateQuery = true;
                    }
                    if (Objects.nonNull(filter)) {
                        scan.setFilter(filter);
                    }
                }
            }
            // 设置启始 rowKey
            if (StringUtils.isNotBlank(hbaseQueryDTO.getStartRowKey())) {
                scan.setStartRow(Bytes.toBytes(hbaseQueryDTO.getStartRowKey()));
            }
            // 设置结束 rowKey
            if (StringUtils.isNotBlank(hbaseQueryDTO.getEndRowKey())) {
                scan.setStopRow(Bytes.toBytes(hbaseQueryDTO.getEndRowKey()));
            }
            // 设置 pageFilter 返回结果在多 region 情况下可能也不准确，通过 limit 限制
            long limit = Objects.isNull(hbaseQueryDTO.getLimit()) ? Long.MAX_VALUE : hbaseQueryDTO.getLimit();
            scan.setMaxResultSize(limit);
            // 单独设置时间戳过滤
            if (Objects.nonNull(timestampFilter)) {
                HbaseClient.fillTimestampFilter(scan, timestampFilter);
            }

            if (!isAccurateQuery) {
                rs = table.getScanner(scan);
                for (Result row : rs) {
                    if (CollectionUtils.isEmpty(row.listCells())) {
                        continue;
                    }
                    results.add(row);
                    if (results.size() >= limit) {
                        break;
                    }
                }
            }

            if (CollectionUtils.isEmpty(results)) {
                return executeResult;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to execute hbase customization,%s", e.getMessage()), e);
        } finally {
            if (hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                close(rs, table, connection);
            } else {
                close(rs, table, null);
            }
            HbaseClient.destroyProperty();
        }
        //理解为一行记录
        for (Result result : results) {
            List<Cell> cells = result.listCells();
            if (CollectionUtils.isEmpty(cells)) {
                continue;
            }
            long timestamp = 0L;
            HashMap<String, Object> row = Maps.newHashMap();
            for (Cell cell : cells) {
                row.put(ROWKEY, Bytes.toString(cell.getRowArray(), cell.getRowOffset(), cell.getRowLength()));
                String family = Bytes.toString(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength());
                String qualifier = Bytes.toString(cell.getQualifierArray(), cell.getQualifierOffset(), cell.getQualifierLength());
                Object value;
                String familyQualifier = String.format(FAMILY_QUALIFIER, family, qualifier);
                if (MapUtils.isNotEmpty(hbaseQueryDTO.getColumnTypes()) && Objects.nonNull(hbaseQueryDTO.getColumnTypes().get(familyQualifier))) {
                    HbaseQueryDTO.ColumnType columnType = hbaseQueryDTO.getColumnTypes().get(familyQualifier);
                    value = convertColumnType(columnType, cell);
                } else {
                    value = Bytes.toString(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
                }
                row.put(familyQualifier, value);
                //取到最新变动的时间
                if (cell.getTimestamp() > timestamp) {
                    timestamp = cell.getTimestamp();
                }
            }
            row.put(TIMESTAMP, timestamp);
            executeResult.add(row);
        }
        return executeResult;
    }


    private static boolean getAccurateQuery(Table table, List<Result> results, Filter filter) throws IOException {
        if (filter instanceof RowFilter) {
            RowFilter rowFilterFilter = (RowFilter) filter;
            if (rowFilterFilter.getOperator().equals(CompareFilter.CompareOp.EQUAL)) {
                Get get = new Get(rowFilterFilter.getComparator().getValue());
                Result r = table.get(get);
                results.add(r);
                return true;
            }
        }
        return false;
    }

    /**
     * 转化 hbase 字段值类型
     *
     * @param columnType 字段值类型
     * @param cell       cell
     * @return 转化后的值
     */
    private Object convertColumnType(HbaseQueryDTO.ColumnType columnType, Cell cell) {
        switch (columnType) {
            case INT:
                return Bytes.toInt(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
            case LONG:
                return Bytes.toLong(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
            case FLOAT:
                return Bytes.toFloat(cell.getValueArray(), cell.getValueOffset());
            case DOUBLE:
                return Bytes.toDouble(cell.getValueArray(), cell.getValueOffset());
            case BOOLEAN:
                return Bytes.toBoolean(cell.getValueArray());
            case HEX:
                return Bytes.toHex(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
            case SHORT:
                return Bytes.toShort(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
            case BIG_DECIMAL:
                return Bytes.toBigDecimal(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
            default:
                return Bytes.toString(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());

        }
    }

    /**
     * 转化 common-loader 定义的 FilterList 为 hbase 中的 FilterList
     *
     * @param loaderFilterList common-loader 定义的 FilterList
     * @param hbaseFilterList  hbase 中 FilterList
     */
    public static void convertFilter(com.dtstack.dtcenter.loader.dto.filter.FilterList loaderFilterList, FilterList hbaseFilterList) {
        List<com.dtstack.dtcenter.loader.dto.filter.Filter> loaderFilters = loaderFilterList.getFilters();
        if (CollectionUtils.isEmpty(loaderFilters)) {
            return;
        }
        for (com.dtstack.dtcenter.loader.dto.filter.Filter filter : loaderFilters) {
            if (filter instanceof com.dtstack.dtcenter.loader.dto.filter.FilterList) {
                com.dtstack.dtcenter.loader.dto.filter.FilterList filterList = (com.dtstack.dtcenter.loader.dto.filter.FilterList) filter;
                FilterList filterNew = new FilterList(convertOp(filterList.getOperator()));
                convertFilter(filterList, filterNew);
                hbaseFilterList.addFilter(filterNew);
            } else {
                Filter filterConvert = FilterType.get(filter);
                if (Objects.nonNull(filterConvert)) {
                    hbaseFilterList.addFilter(filterConvert);
                }
            }
        }
    }

    /**
     * 转化 FilterList.Operator
     *
     * @param operator common-loader 中的 FilterList.Operator
     * @return hbase 的 FilterList.Operator
     */
    public static FilterList.Operator convertOp(com.dtstack.dtcenter.loader.dto.filter.FilterList.Operator operator) {
        if (operator.equals(com.dtstack.dtcenter.loader.dto.filter.FilterList.Operator.MUST_PASS_ONE)) {
            return FilterList.Operator.MUST_PASS_ONE;
        }
        return FilterList.Operator.MUST_PASS_ALL;
    }

    /**
     * 关闭hbase连接：当connection不为null且没有开启连接池或者开启kerberos的情况下进行关闭hbase连接
     *
     * @param connection     hbase连接
     * @param hbaseSourceDTO hbase数据源信息
     */
    private static void closeConnection(Connection connection, HbaseSourceDTO hbaseSourceDTO) {
        if (connection != null && (hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig()))) {
            try {
                connection.close();
            } catch (IOException e) {
                log.error("hbase Close connection exception", e);
            }
        }
    }

    /**
     * 关闭admin、table、resultScanner......
     *
     * @param closeables 可关闭连接、结果集
     */
    private void close(Closeable... closeables) {
        try {
            if (Objects.nonNull(closeables)) {
                for (Closeable closeable : closeables) {
                    if (Objects.nonNull(closeable)) {
                        closeable.close();
                    }
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("hbase can not close table error,%s", e.getMessage()), e);
        }
    }

    //获取表命名空间的方法
    @Override
    public List<JSONObject> getAllNameSpaces(ISourceDTO iSource) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<JSONObject> namespaceDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            NamespaceDescriptor[] listNamespaceDescriptors = admin.listNamespaceDescriptors();
            List<NamespaceDescriptor> namespaceDescriptors1 = Arrays.asList(listNamespaceDescriptors);
            for (NamespaceDescriptor namespaceDescriptor : namespaceDescriptors1) {

                // 获取命名空间的 ACL
                String acl = namespaceDescriptor.getConfigurationValue("hbase.acl");

                // 解析 ACL 并提取权限信息
                if (acl != null && !acl.isEmpty()) {
                    String[] permissions = acl.split(",");
                    for (String permission : permissions) {
                        System.out.println(permission.trim());
                        // 在此处可以进一步解析权限信息，提取所需的权限数据
                    }
                }
                String s = JSONObject.toJSONString(namespaceDescriptor);
                JSONObject jsonObject = JSONObject.parseObject(s);
                namespaceDescriptors.add(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("hbase can not get namespaces error,%s", e.getMessage()), e);
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return namespaceDescriptors;

    }

    @Override
    public List<JSONObject> getNameSpacesByName(ISourceDTO iSource, String namespaceName) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<JSONObject> namespaceDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            NamespaceDescriptor listNamespaceDescriptors = admin.getNamespaceDescriptor(namespaceName);
            List<NamespaceDescriptor> namespaceDescriptors1 = Arrays.asList(listNamespaceDescriptors);
            for (NamespaceDescriptor namespaceDescriptor : namespaceDescriptors1) {

                // 获取命名空间的 ACL
                String acl = namespaceDescriptor.getConfigurationValue("hbase.acl");

                // 解析 ACL 并提取权限信息
                if (acl != null && !acl.isEmpty()) {
                    String[] permissions = acl.split(",");
                    for (String permission : permissions) {
                        System.out.println(permission.trim());
                        // 在此处可以进一步解析权限信息，提取所需的权限数据
                    }
                }
                String s = JSONObject.toJSONString(namespaceDescriptor);
                JSONObject jsonObject = JSONObject.parseObject(s);
                namespaceDescriptors.add(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("hbase can not get namespaces error,%s", e.getMessage()), e);
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return namespaceDescriptors;

    }

    //创建命名空间
    @Override
    public Boolean createNameSpaces(ISourceDTO iSource, List<JSONObject> namespaceDescriptors) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            NamespaceDescriptor[] namespaceDescriptorsTarget = admin.listNamespaceDescriptors();
            List<NamespaceDescriptor> namespaceDescriptorsListTarget = new ArrayList<>();
            if (0 != namespaceDescriptorsTarget.length) {
                namespaceDescriptorsListTarget = Arrays.asList(namespaceDescriptorsTarget);
            }
            List<JSONObject> needCreateSpace = getNeedCreateSpace(namespaceDescriptors, namespaceDescriptorsListTarget);
            for (JSONObject namespaceDescriptorJson : needCreateSpace) {
                // 创建Gson对象
                Gson gson = new Gson();

                // 将JSON字符串转换为自定义对象
                NamespaceDescriptor namespaceDescriptor = gson.fromJson(namespaceDescriptorJson.toJSONString(), NamespaceDescriptor.class);

                // 打印自定义对象
                System.out.println(namespaceDescriptor);
                if (!"hbase".equalsIgnoreCase(namespaceDescriptor.getName()) && !"default".equalsIgnoreCase(namespaceDescriptor.getName())) {
                    admin.createNamespace(namespaceDescriptor);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;

        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return true;

    }

    public List<JSONObject> getNeedCreateSpace(List<JSONObject> namespaceDescriptors, List<NamespaceDescriptor> namespaceDescriptorsListTarget) {

        List<JSONObject> namespaces = namespaceDescriptors.stream().
                filter(source -> namespaceDescriptorsListTarget.stream()
                        .noneMatch(target -> source.getString("name").equals(target.getName()))).collect(Collectors.toList());
        return namespaces;

    }

    @Override
    public List<Object> getAllTableDescriptor(ISourceDTO iSource) {

        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<Object> tableDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            HTableDescriptor[] hTableDescriptors = admin.listTables();
            tableDescriptors = Arrays.asList(hTableDescriptors);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return tableDescriptors;
    }


    @Override
    public List<Object> getTableDescriptorByTableName(ISourceDTO iSource, String tableName) throws Exception {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<Object> tableDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            TableName table = TableName.valueOf(tableName);
            HTableDescriptor tableDescriptor = admin.getTableDescriptor(table);
            tableDescriptors.add(tableDescriptor);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return tableDescriptors;
    }

    @Override
    public Boolean tableExist(ISourceDTO iSource, String tableName) throws Exception {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            TableName table = TableName.valueOf(tableName);
            boolean b = admin.tableExists(table);
            return b;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
    }

    @Override
    public Boolean createTableDescriptor(ISourceDTO iSource, List<Object> tableDescriptors) throws Exception {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            for (Object tableDescriptor : tableDescriptors) {
                //判断表是否存在
                HTableDescriptor tableDes = (HTableDescriptor) tableDescriptor;
                boolean b = admin.tableExists(TableName.valueOf(tableDes.getTableName().getName()));
                if (!b) {
                    admin.createTable(tableDes);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return true;
    }

    @Override
    public Boolean createTableSnapshot(ISourceDTO iSource, String tableName, String snapshotName) throws Exception {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            TableName tabName = TableName.valueOf(tableName);
            log.info("开始创建快照 表:【" + tableName + "】" + ", 快照：【" + snapshotName + "】");
            //创建表快照
            admin.snapshot(snapshotName, tabName);
            List<HBaseProtos.SnapshotDescription> snapshotDescriptions = admin.listSnapshots(snapshotName);
            HBaseProtos.SnapshotDescription snapshotDescription = snapshotDescriptions.get(0);
            //判断快照是否已经创建成功
            log.info("创建完成查询快照是否存在  快照：【" + snapshotName + "】");
            boolean snapshotFinished = admin.isSnapshotFinished(snapshotDescription);
            admin.close();
            log.info("快照创建结束");
            return snapshotFinished;
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }

            }
        }

    }

    @Override
    public String exportSnapshot(ISourceDTO iSource, String snapshotName, String hbaseExportPath, String resourceJarPath, String callBackUrl, String logOutPutPath) throws Exception {
        try {
            HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
            Map<String, Object> sourceToMap = HbasePoolManager.sourceToMap(iSource, null);
            Configuration hConfig = HBaseConfiguration.create();
            for (Map.Entry<String, Object> entry : sourceToMap.entrySet()) {
                if (null == entry.getValue()) {
                    hConfig.set(entry.getKey(), "");
                } else {
                    hConfig.set(entry.getKey(), (String) entry.getValue());
                }

            }
            hConfig.set("mapreduce.app-submission.cross-platform", "true");
            hConfig.set("mapred.jar", resourceJarPath);
            DsgExportSnapshot dsgExportSnapshot = new DsgExportSnapshot(callBackUrl, logOutPutPath);
            String hbaseSnapSourcePath = String.valueOf(sourceToMap.get("hbase.rootdir"));
            String defaultFS = String.valueOf(sourceToMap.get("fs.defaultFS"));
            Map<String, Object> kerberosConfig = hbaseSourceDTO.getKerberosConfig();
            if (MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                hConfig.set("ipc.client.fallback-to-simple-auth-allowed", "true");
                String krb5Conf = MapUtils.getString(kerberosConfig, HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                // 加载 krb5.conf 文件
                File krb5File = new File(krb5Conf);
                Properties krb5Properties = new Properties();
                krb5Properties.load(new FileInputStream(krb5File));
                String defaultRealm = krb5Properties.getProperty("default_realm");
                System.setProperty("java.security.krb5.realm", defaultRealm);
                // 获取 KDC 主机名
                String kdcHostname = krb5Properties.getProperty("kdc");
                System.setProperty("java.security.krb5.kdc", kdcHostname);
                System.setProperty("java.security.krb5.conf", krb5Conf);
                kerberosConfig.put(HadoopConfTool.HADOOP_SECURITY_AUTHORIZATION, "kerberos");
                return KerberosLoginUtil.loginWithUGI(kerberosConfig).doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        FileSystem fileSystem1 = FileSystem.newInstance(hConfig);
                        String s = HAUtil.getAddressOfActive(fileSystem1).toString();
                        System.out.println("活跃节点" + s);
                        String[] split = s.split("/");
                        String s1 = split[1];
                        System.out.println("活跃节点" + s1);
                        dsgExportSnapshot.setConf(hConfig);
                        String replace = hbaseSnapSourcePath.replace(defaultFS, "hdfs://" + s1);
                        //TODO 低版本时需要获取到hbase.rootdir 将高可用的地址换成活跃节点
                        String[] strings = new String[]{"-snapshot", snapshotName, "-copy-to", hbaseExportPath, "-copy-from", replace, "-mappers", "16"};
                        dsgExportSnapshot.start(strings);
                        jobID = dsgExportSnapshot.getJobID();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });


            } else {
                return KerberosLoginUtil.loginWithUGI(kerberosConfig, "hbase").doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        FileSystem fileSystem1 = FileSystem.newInstance(hConfig);
                        String s = HAUtil.getAddressOfActive(fileSystem1).toString();
                        System.out.println("活跃节点" + s);
                        String[] split = s.split("/");
                        String s1 = split[1];
                        System.out.println("活跃节点" + s1);
                        dsgExportSnapshot.setConf(hConfig);
                        String replace = hbaseSnapSourcePath.replace(defaultFS, "hdfs://" + s1);
                        //TODO 低版本时需要获取到hbase.rootdir 将高可用的地址换成活跃节点
                        String[] strings = new String[]{"-snapshot", snapshotName, "-copy-to", hbaseExportPath, "-copy-from", replace, "-mappers", "16"};
                        dsgExportSnapshot.start(strings);
                        jobID = dsgExportSnapshot.getJobID();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });
            }


        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Boolean doRestoreHbase(ISourceDTO iSource, String tabName, String snapshotName) throws Exception {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            log.info("查询快照是否存在 表:【" + tabName + "】" + ", 快照：【" + snapshotName + "】");
            //查询快照是否存在
            AtomicReference<List<HBaseProtos.SnapshotDescription>> snapshotDescriptions = new AtomicReference<>(admin.listSnapshots(snapshotName));
            if (snapshotDescriptions.get().size() == 0) {
                long startTime = System.currentTimeMillis();
                long endTime = startTime + 20000; // 20 seconds later
                while (System.currentTimeMillis() < endTime) {
                    // 在这里编写要执行的任务逻辑
                    try {
                        snapshotDescriptions.set(admin.listSnapshots(snapshotName));
                        if (snapshotDescriptions.get().size() != 0) {
                            break;
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                        break;
                    }
                }
                if (snapshotDescriptions.get().size() == 0) {
                    throw new RuntimeException("当前快照：【" + snapshotName + "】不存在");
                }

            }
            HBaseProtos.SnapshotDescription snapshotDescription = snapshotDescriptions.get().get(0);
            boolean snapshotFinished = admin.isSnapshotFinished(snapshotDescription);
            if (!snapshotFinished) {
                throw new RuntimeException("当前快照：【" + snapshotName + "】不存在");
            }
            log.info("禁用表:【" + tabName + "】");
            //禁用表
            TableName tableName = TableName.valueOf(tabName);
            //禁用表
            admin.disableTable(tableName);

            boolean tableDisabled = admin.isTableDisabled(tableName);
            //恢复快照
            if (tableDisabled) {
                log.info("禁用表:【" + tabName + "】成功");
                admin.restoreSnapshot(snapshotName);
                log.info("恢复快照:【" + snapshotName + "】成功");
            } else {
                log.info("禁用表:【" + tabName + "】失败");
            }
            //启用表
            admin.enableTable(tableName);
            log.info("启用表:【" + tabName + "】成功");
            //删除快照
            admin.deleteSnapshot(snapshotName);
            log.info("删除快照:【" + snapshotName + "】成功");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }

            }
        }

    }

    @Override
    public Boolean deleteHbaseSnap(ISourceDTO iSource, String snapshotName) throws Exception {
        Boolean result = true;
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO);
            admin = hConn.getAdmin();
            List<HBaseProtos.SnapshotDescription> snapshotDescriptions = admin.listSnapshots(snapshotName);
            if (snapshotDescriptions.size() != 0) {
                HBaseProtos.SnapshotDescription snapshotDescription = snapshotDescriptions.get(0);
                //判断快照是否已经创建成功
                boolean snapshotFinished = admin.isSnapshotFinished(snapshotDescription);
                if (snapshotFinished) {
                    try {
                        admin.deleteSnapshot(snapshotName);
                    } catch (Exception e) {
                        e.printStackTrace();
                        result = false;
                        throw new RuntimeException(e.getMessage());
                    }
                } else {
                    result = false;
                    throw new RuntimeException("快照【" + snapshotName + "】不存在");
                }
            } else {
                result = false;
                throw new RuntimeException("快照【" + snapshotName + "】不存在");
            }
            admin.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != hConn) {
                try {
                    hConn.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }


        return false;
    }

    @Override
    public String exportData(ISourceDTO iSource, String tableName, String hbaseExportPath, String resourceJarPath, String startTime, String endTime, String callBackUrl, String logOutPutPath) throws Exception {
        try {
            HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
            Map<String, Object> sourceToMap = HbasePoolManager.sourceToMap(iSource, null);
            Configuration hConfig = HBaseConfiguration.create();
            for (Map.Entry<String, Object> entry : sourceToMap.entrySet()) {
                if (null == entry.getValue()) {
                    hConfig.set(entry.getKey(), "");
                } else {
                    hConfig.set(entry.getKey(), (String) entry.getValue());
                }
            }
            hConfig.set("mapreduce.app-submission.cross-platform", "true");
            hConfig.set("mapred.jar", resourceJarPath);
            Map<String, Object> kerberosConfig = hbaseSourceDTO.getKerberosConfig();
            if (MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                hConfig.set("ipc.client.fallback-to-simple-auth-allowed", "true");
                String krb5Conf = MapUtils.getString(kerberosConfig, HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                // 加载 krb5.conf 文件
                File krb5File = new File(krb5Conf);
                Properties krb5Properties = new Properties();
                krb5Properties.load(new FileInputStream(krb5File));
                String defaultRealm = krb5Properties.getProperty("default_realm");
                System.setProperty("java.security.krb5.realm", defaultRealm);
                // 获取 KDC 主机名
                String kdcHostname = krb5Properties.getProperty("kdc");
                System.setProperty("java.security.krb5.kdc", kdcHostname);
                System.setProperty("java.security.krb5.conf", krb5Conf);
                kerberosConfig.put(HadoopConfTool.HADOOP_SECURITY_AUTHORIZATION, "kerberos");
                return KerberosLoginUtil.loginWithUGI(kerberosConfig).doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        Job job = DsgExportData.submitTableJob(hConfig, tableName, hbaseExportPath, startTime, endTime, callBackUrl, logOutPutPath);
                        jobID = job.getJobID().toString();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });
            } else {
                return KerberosLoginUtil.loginWithUGI(kerberosConfig, "hbase").doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        Job job = DsgExportData.submitTableJob(hConfig, tableName, hbaseExportPath, startTime, endTime, callBackUrl, logOutPutPath);
                        jobID = job.getJobID().toString();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public String importData(ISourceDTO iSource, String tableName, String hbaseImportPath, String resourceJarPath, String callBackUrl, String logOutPutPath) throws Exception {
        try {
            HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
            Map<String, Object> sourceToMap = HbasePoolManager.sourceToMap(iSource, null);
            Configuration hConfig = HBaseConfiguration.create();
            for (Map.Entry<String, Object> entry : sourceToMap.entrySet()) {
                if (null == entry.getValue()) {
                    hConfig.set(entry.getKey(), "");
                } else {
                    hConfig.set(entry.getKey(), (String) entry.getValue());
                }
            }
            hConfig.set("mapreduce.app-submission.cross-platform", "true");
            hConfig.set("mapred.jar", resourceJarPath);
            Map<String, Object> kerberosConfig = hbaseSourceDTO.getKerberosConfig();
            if (MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                hConfig.set("ipc.client.fallback-to-simple-auth-allowed", "true");
                String krb5Conf = MapUtils.getString(kerberosConfig, HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                // 加载 krb5.conf 文件
                File krb5File = new File(krb5Conf);
                Properties krb5Properties = new Properties();
                krb5Properties.load(new FileInputStream(krb5File));
                String defaultRealm = krb5Properties.getProperty("default_realm");
                System.setProperty("java.security.krb5.realm", defaultRealm);
                // 获取 KDC 主机名
                String kdcHostname = krb5Properties.getProperty("kdc");
                System.setProperty("java.security.krb5.kdc", kdcHostname);
                System.setProperty("java.security.krb5.conf", krb5Conf);
                kerberosConfig.put(HadoopConfTool.HADOOP_SECURITY_AUTHORIZATION, "kerberos");
                return KerberosLoginUtil.loginWithUGI(kerberosConfig).doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        Job job = DsgImportData.submitTableJob(hConfig, tableName, hbaseImportPath, callBackUrl, logOutPutPath);
                        jobID = job.getJobID().toString();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });
            } else {
                return KerberosLoginUtil.loginWithUGI(kerberosConfig, "hbase").doAs((PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        Job job = DsgImportData.submitTableJob(hConfig, tableName, hbaseImportPath, callBackUrl, logOutPutPath);
                        jobID = job.getJobID().toString();
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    return jobID;
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }


}
