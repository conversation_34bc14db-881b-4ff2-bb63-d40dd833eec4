package com.dtstack.dtcenter.common.loader.hbase.util;

import org.apache.hadoop.mapreduce.Job;
import org.apache.log4j.FileAppender;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.apache.log4j.PatternLayout;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/8/16 14:45
 */
public class DsgHbaseUtil {
    public static void setCustomLogOutput(Job job, String logOutputPath) throws IOException {
        // 获取作业的Logger
        Logger logger = Logger.getLogger(job.getClass());
        logger.setLevel(Level.INFO); // 设置日志级别，INFO代表输出INFO及以上级别的日志

        // 创建自定义的Log4j Appender
        FileAppender fileAppender = new FileAppender();
        fileAppender.setFile(logOutputPath);
        fileAppender.setLayout(new PatternLayout("%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n"));
        fileAppender.setThreshold(Level.INFO); // 设置日志级别，INFO代表输出INFO及以上级别的日志
        fileAppender.activateOptions();

        // 将自定义的Appender添加到Logger中
        logger.addAppender(fileAppender);

        // 设置YARN的日志输出
        job.getConfiguration().set("mapreduce.task.files.preserve.filepattern", logOutputPath);
    }
}