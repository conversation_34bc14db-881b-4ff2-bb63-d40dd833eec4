package com.dsg.database.datasource.enums;

import lombok.Getter;

/**
 * hadoop配置
 *
 * <AUTHOR>
 * @date 2022-07-08
 */
public enum HadoopConfig {

    /**
     * HDFS 配置
     */
    HDFS_DEFAULTFS("defaultFS"),
    /**
     * fs.defaultFS 配置
     */
    HDFS_FS_DEFAULTFS("fs.defaultFS"),

    /**
     * HDFS 正则
     */
    DEFAULT_FS_REGEX("hdfs://.*"),

    /**
     * 高可用配置
     */
    HADOOP_CONFIG("hadoopConfig"),

    /**
     * core 配置
     */
    CORE_SITE("coreSiteUrl"),
    /**
     * hive 配置
     */
    HIVE_SITE("hiveSiteUrl"),

    /**
     * hdfs 配置
     */
    HDFS_SITE("hdfsSiteUrl"),
    /**
     * hbase 配置
     */
    HBASE_SITE("hbaseSiteUrl"),

    /**
     * hbase 配置
     */
    HIVE_CLIENT("hiveClientUrl"),

    /**
     * hdfs 远程用户
     */
    HDFS_REMOTE_USER("remoteUser"),
    ;

    @Getter
    private String val;

    HadoopConfig(String val) {
        this.val = val;
    }

}
