package com.dsg.database.datasource.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/10/8
 * @create 2022-10-08-15:13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ConsumerInfoDTO {
    private String id;
    private String node;
    private String group;
    private Integer activeThreads;
    private Integer activeTopics;
}
