package com.dsg.database.datasource.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: OffsetInfoDTo
 * @Function: TODO
 * @Date: 2023/2/3 17:25
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class OffsetInfoDTO {
    private int partition;
    private long logSize;
    private long offset;
    private long lag;
    private String owner;
    private String create;
    private String modify;
    private String groupId;
}
