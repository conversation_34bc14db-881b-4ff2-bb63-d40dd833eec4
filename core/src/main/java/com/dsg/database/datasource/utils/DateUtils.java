package com.dsg.database.datasource.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DateUtils
 * @Function: TODO
 * @Date: 2023/2/1 17:02
 */
public class DateUtils {
    /** Get custom date,like yyyy/mm/dd etc. */
    public static String getCustomDate(String formatter) {
        SimpleDateFormat df = new SimpleDateFormat(formatter);
        return df.format(new Date());
    }
    /** Get unix time. */
    public static long getTimeSpan() {
        return new Date().getTime();
    }
    /**
     * Convert unix time to formatter date.
     *
     * @param unixtime
     * @param formatter
     * @return Date String.
     */
    public static String convertUnixTime(long unixtime, String formatter) {
        SimpleDateFormat df = new SimpleDateFormat(formatter);
        return df.format(new Date(unixtime));
    }

    /**
     * Convert unix time to date,default is yyyy-MM-dd HH:mm:ss.
     *
     * @param unixtime
     * @return Date String.
     */
    public static String convertUnixTime(long unixtime) {
        return convertUnixTime(unixtime, "yyyy-MM-dd HH:mm:ss");
    }
    public static String getDate() {
        SimpleDateFormat df = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }
}
