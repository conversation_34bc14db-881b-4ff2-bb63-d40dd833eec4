package com.dsg.database.datasource.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * jdbc result set 相关信息
 *
 * <AUTHOR>
 * @date 2023/7/19 18:17
 */
@Builder
@Data
public class ConnectorResultEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Connection connection;
    private PreparedStatement preparedStatement;
    private ResultSet resultSet;
    private Long total;

    public void close() {
        try {
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (resultSet != null) {
                resultSet.close();
            }
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}