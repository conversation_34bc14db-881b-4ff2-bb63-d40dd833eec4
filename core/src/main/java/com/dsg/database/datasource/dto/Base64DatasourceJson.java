package com.dsg.database.datasource.dto;

import lombok.Data;

/**
 * 数据源加密对象
 */
@Data
public class Base64DatasourceJson {

    /**
     * 数据源名称
     */
    String dataName;

    /**
     * 数据源类型
     */
    String dataType;

    /**
     * 枚举编码
     */
    Integer dataTypeCode;

    /**
     * 驱动
     */
    String driverClassName;

    /**
     * 驱动版本
     */
    String dataVersion;

    /**
     * 用户名
     */
    String username;

    /**
     * 密码
     */
    String password;
    /**
     * 连接串
     */
    String jdbcUrl;

    /**
     * 业务系统
     */
    String businessUuid;

    /**
     * 展示数据源类型
     */
    String showDataType;


    /**
     * 字符集
     */
    String characterSet;

    /**
     * 时区
     */
    String dsTimeZone;
    /**
     * 数据库版本
     */
    String dbVersion;

    /**
     * 模式
     */
    String schema;
    /**
     * url连接方式
     */
    String UrlType;


    /**
     * ftp sftp 用的协议
     */
    String protocol;
    /**
     * ftp sftp host
     */
    String host;

    /**
     * ftp sftp port
     */
    Long port;
    /**
     * 用户名
     */
    private String accessKey;
    /**
     * 密码
     */
    private String secretKey;

    /**
     * 连接地址
     */
    private String endPoint;
    /**
     * 区域
     */
    private String region;

    /**
     * 桶名称
     */
    private String bucket;

}
