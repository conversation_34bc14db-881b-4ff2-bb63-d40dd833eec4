package com.dsg.database.datasource.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/9/27
 * @create 2022-09-27-15:09
 */
public class NetUtils {
    public static final Logger Log = LoggerFactory.getLogger(NetUtils.class);
    public static final int TIME_OUT = 3000;
    public static final int BUFFER_SIZE = 8049;

    public static boolean telnet(String host, int port) {
        if (port == -1) {
            return false;
        }
        Socket socket = new Socket();
        try {
            socket.setReceiveBufferSize(BUFFER_SIZE);
            socket.setSoTimeout(TIME_OUT);
        } catch (Exception ex) {
            Log.error("Socket create failed.");
        }
        SocketAddress address = new InetSocketAddress(host, port);
        try {
            socket.connect(address, TIME_OUT);
            return true;
        } catch (IOException e) {
            Log.error("Telnet [" + host + ":" + port + "] has crash, please check it.");
            return false;
        } finally {
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e) {
                    Log.error("Close socket [" + host + ":" + port + "] has error.");
                }
            }
        }
    }
}
