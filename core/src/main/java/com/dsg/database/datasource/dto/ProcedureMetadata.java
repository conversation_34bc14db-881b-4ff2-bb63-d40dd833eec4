package com.dsg.database.datasource.dto;

import lombok.Data;

import java.sql.Date;

/**
 * 元数据-存储过程对象 procedure_metadata
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Data
public class ProcedureMetadata {

    private static final long serialVersionUID = 1L;

    /**
     * 存储过程id
     */
    private Long procedureId;

    /**
     * 存储过程名称
     */
    private String objectName;

    /**
     * 对象类型（应该是'procedure'）
     */
    private String objectType;

    /**
     * 状态（有效/无效）
     */
    private String status;

    /**
     * 存储过程创建日期
     */
    private Date created;

    /**
     * 最后ddl时间
     */
    private Date lastDdlTime;

    /**
     * 存储过程源代码
     */
    private String sourceCode;

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String datasourceType;
    /**
     * 库名
     */
    private String dbName;

}
