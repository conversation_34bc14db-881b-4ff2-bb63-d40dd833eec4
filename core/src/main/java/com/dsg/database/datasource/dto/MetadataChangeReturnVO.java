package com.dsg.database.datasource.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * MetadataChange
 *
 * <AUTHOR>
 * @date 2023-05-25
 */
@Data
public class MetadataChangeReturnVO {
    /**
     * 操作方式:提供给元数据表使用insert、update、delete
     */
    private String opt;
    /**
     * 元数据原表表实体
     */
    private MetadataChangeTable sourceMetadataTableVO;

    /**
     * 元数据目标表表实体
     */
    private MetadataChangeTable targetMetadataTableVO;

    /**
     * 元数据表变更实体
     */
    private List<MetadataAttributeVO> updateTableMetadata;

    /**
     * 元数据字段变更实体
     */
    private List<MetadataAttributeColVO> updateColMetadata;
    /**
     * 元数据字段新增实体
     */
    private List<MetadataChangeColumn> insertMetadataColumnVO;
    /**
     * 元数据字段删除实体
     */
    private List<MetadataChangeColumn> deleteMetadataColumnVO;
    /**
     * 操作信息
     */
    private String msg;

    /**
     * 租户
     */
    private String tenantCode;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 部门Id
     */
    private String dedpId;

    /**
     * 更新时间
     */
    private Date updateTime;

}
