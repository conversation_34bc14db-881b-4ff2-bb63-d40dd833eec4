package com.dsg.database.datasource.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 元数据字段 对象 metadata_column
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Data
public class MetadataChangeColumn {

    private static final long serialVersionUID = 1L;

    /**
     * 字段唯一标识
     */
    private String metadataColumnUuid;

    /**
     * 表id
     */
    private String metadataTableUuid;

    /**
     * 字段名称
     */
    private String columnName;

    /**
     * 字段中文名称
     */
    private String columnCnName;

    /**
     * 字段别名
     */
    private String columnAlias;

    /**
     * 字段排序
     */
    private Long columnSort;

    /**
     * 字段长度
     */
    private String columnLength;

    /**
     * 字段类型
     */
    private String columnType;

    /**
     * 字段注释
     */
    private String columnComment;

    /**
     * 是否是主键字段 0不是1是
     */
    private String columnPrimaryKey;

    /**
     * 是否是外键 0不是1是
     */
    private String columnForeignKey;

    /**
     * 是否有唯一约束 0不是1是
     */
    private String columnUnique;

    /**
     * 是否为空 0不是1是
     */
    private String columnNull;

    /**
     * 是否统计项 0不是1是
     */
    private String columnCount;

    /**
     * 是否搜索项 0不是1是
     */
    private String columnSerach;

    /**
     * 是否排序项 0不是1是
     */
    private String columnSortFlag;

    /**
     * 是否增量字段 0不是1是
     */
    private String columnIncremental;

    /**
     * 字段默认值
     */
    private String columnDefaultValue;

    /**
     * 索引约束名
     */
    private String columnIndexConstraintName;

    /**
     * 列标签
     */
    private String columnLabel;

    /**
     * 数据格式
     */
    private String columnDataFormat;

    /**
     * 数据类型
     */
    private String columnDataType;

    /**
     * SQL类型
     */
    private String dataType;

    /**
     * 数据精度
     */
    private String columnScale;
}
