package com.dsg.database.datasource.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 元数据检核映射实体
 *
 * <AUTHOR>
 * @date 2023/6/2 16:08
 */
@Data
public class MetadataAttributeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *元数据类型:table、column
     */
    private String type;
    /**
     *字段变更属性
     */
    private String columnChangeAttribute;
    /**
     *数据库中的元数据
     */
    private Object targetMeta;
    /**
     *来源系统的元数据
     */
    private Object sourceMeta;


}
