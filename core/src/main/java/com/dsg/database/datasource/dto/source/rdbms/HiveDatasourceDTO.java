package com.dsg.database.datasource.dto.source.rdbms;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO Hive-1.x数据源配置参数
 *
 * <AUTHOR>
 * @date 2022/7/13 15:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HiveDatasourceDTO extends JdbcDatasourceDTO {

    private static final long serialVersionUID = 1L;

    /**
     * defaultFS
     * 示例：hdfs://host:port
     */
    private String defaultFS;
    /**
     * 高可用配置
     * 示例：{
     * "dfs.nameservices": "defaultDfs",
     * "dfs.ha.namenodes.defaultDfs": "namenode1",
     * "dfs.namenode.rpc-address.defaultDfs.namenode1": "",
     * "dfs.client.failover.proxy.provider.defaultDfs": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider"
     * }
     * 高可用模式下的填写规则：
     * 1、分别要填写：nameservice名称、 namenode名称（多个以逗号分隔）、proxy.provider参数；
     * 2、所有参数以JSON格式填写；
     * 3、格式为：
     * "dfs.nameservices": "nameservice名称", "dfs.ha.namenodes.nameservice名称": "namenode名称，以逗号分隔", "dfs.namenode.rpc-address.nameservice名称.namenode名称": "", "dfs.namenode.rpc-address.nameservice名称.namenode名称": "", "dfs.client.failover.proxy.provider.
     * nameservice名称": "org.apache.hadoop.
     * hdfs.server.namenode.ha.
     * ConfiguredFailoverProxyProvider"
     * 4、详细参数含义请参考《帮助文档》或<a href='http://hadoop.apache.org/docs/r2.7.4/hadoop-project-dist/hadoop-hdfs/HDFSHighAvailabilityWithQJM.html'>Hadoop官方文档</a>
     */
    private String hadoopConfig;
    /**
     * 开启Kerberos认证
     */
    private String openKerberos;
}
