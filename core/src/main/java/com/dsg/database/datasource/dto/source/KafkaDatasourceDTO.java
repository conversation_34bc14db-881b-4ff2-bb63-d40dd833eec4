package com.dsg.database.datasource.dto.source;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * Kafka数据源配置参数
 *
 * <AUTHOR>
 * @date 2022/7/18 18:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KafkaDatasourceDTO extends BaseDatasourceDTO {

    private static final long serialVersionUID = 1L;

    /**
     * ZK 的地址
     */
    private String address;

    /**
     * kafka Brokers 的地址
     */
    private String brokerList;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * kerberos 配置信息
     */
    private Map<String, Object> kerberosConfig;
}
