package com.dsg.database.datasource.utils;

import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public final class Strings {
    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";
    /**
     * null字符串
     */
    public static final char BLANK_CHAR = ' ';
    public static final String EMPTY = "";
    private static final String REFERENCE = "{}";

    private Strings() {
    }

    /**
     * 判断字符串值是否为null
     *
     * @param string 需进行判断的字符串
     * @return 若字符串值为null, 则返回true, 否则返回false
     */
    public final static boolean isNull(String string) {
        return Objects.isNull(string);
    }

    /**
     * 判断字符串是否为null
     *
     * @param string 需进行判断的字符串
     * @return 若字符串部位null, 则返回true, 否则返回false
     */
    public final static boolean isNotNull(String string) {
        return !isNull(string);
    }

    /**
     * 判断字符串是否为空串(字符串非null,并且长度为0)
     *
     * @param string 需进行判断的字符串
     * @return 若字符串长度为0, 则返回true, 否则返回false
     */
    public final static boolean isEmpty(final String string) {
        return isNotNull(string) && string.length() == 0;
    }

    /**
     * 配租单字符串是否为null或空字符串
     *
     * @param string 需进行判断的字符串
     * @return 如字符串为null或者长度为0, 则返回true, 否则返回false
     */
    public final static boolean isNullOrEmpty(final String string) {
        return isNull(string) || isEmpty(string);
    }

    /**
     * 判断字符串不为空串(字符串非null,并且长度大于0)
     *
     * @param string 需进行判断的字符串
     * @return 若字符串不为空串(字符串长度小于0), 则返回true, 否则返回false
     */
    public final static boolean isNotEmpty(final String string) {
        return isNotNull(string) && string.length() > 0;
    }

    /**
     * 判断字符串是否为空白字符串(字符串不为null,并且为空字符串或者组成字符全部为空格符)
     *
     * @param string 需进行判断的字符串
     * @return 若字符串为空串(" ", " "), 则返回true, 否则返回false
     */
    public final static boolean isBlank(final String string) {
        return isNotNull(string) && string.trim().length() == 0;
    }

    /**
     * 判断字符串是否为空白字符串
     *
     * @param string 需进行判断的字符串
     * @return 若字符串不为空串, 则返回true, 否则返回false
     */
    public final static boolean isNotBlank(final String string) {
        return isNotNull(string) && string.trim().length() > 0;
    }

    /**
     * 字符重复
     *
     * @param ch     字符
     * @param repeat 重复次数
     * @return 返回repeat个ch组成的字符串
     */
    public static final String repeat(char ch, int repeat) {
        Objects.requireNonNull(ch);

        if (repeat > 0) {
            char[] buf = new char[repeat];
            for (int k = 0; k < repeat; k++) {
                buf[k] = ch;
            }
            return new String(buf);
        } else {
            return EMPTY_STRING;
        }
    }


    /**
     * 判断在字符串中是否包含中文字符
     *
     * @param text 需进行判断的字符串
     * @return 如字符串不含有中文字符(不包含中文标点), 则返回aflse
     */
    public final static boolean hasChinese(String text) {
        if (Objects.isNull(text)) {
            return false;
        }
        for (int k = 0; k < text.length(); k++) {
            if (isChinese(text.charAt(k))) {
                return true;
            }
        }
        return false;
    }

    public static final boolean isChinese(char c) {
        return c >= 19968 && c <= '龥';
    }


    /**
     * 后去第一个非空字符串的值
     *
     * @param values 给定的值列表
     * @return
     */
    public static String value(String... values) {
        if (values != null && values.length > 0) {
            for (String value : values) {
                if (isNotBlank(value)) {
                    return value;
                }
            }
        }
        return null;
    }


    /***
     * 根据指定分隔符分割字符串---忽略在引号 和 括号 里面的分隔符
     * @param str
     * @param delimter
     * @return
     */
    public static String[] splitIgnoreQuotaBrackets(String str, String delimter) {
        String splitPatternStr = delimter + "(?![^()]*+\\))(?![^{}]*+})(?![^\\[\\]]*+\\])(?=(?:[^\"]|\"[^\"]*\")*$)";
        return str.split(splitPatternStr);
    }

    public static String format(String format, Object... objects) {
        Objects.requireNonNull(format);

        StringBuilder sbuf = new StringBuilder(format.length() + 60);
        if (Objects.nonNull(objects)) {
            int i = 0, k = 0;
            for (Object object : objects) {
                k = format.indexOf(REFERENCE, i);
                if (k == -1) {
                } else {
                    sbuf.append(format, i, k)
                            .append(toString(object));
                    i = k + 2;
                }
            }
            if (format.length() > i) {
                sbuf.append(format.substring(i));
            }
        }
        return sbuf.toString();
    }

    public static String toString(Object object) {
        if (null == object) {
            return "";
        } else if (object.getClass().isArray()) {
            StringBuilder sb = new StringBuilder();
            int len = Array.getLength(object);
            for (int k = 0; k < len; k++) {
                sb.append(toString(Array.get(object, k))).append(',');
            }
            return sb.length() > 0 ? sb.substring(0, sb.length() - 1) : sb.toString();
        } else {
            return object.toString();
        }
    }

    /**
     * 根据指定分隔符分割字符串---忽略在引号里面的分隔符
     */
    public static List<String> splitIgnoreQuota(String sqls, char delimiter) {
        List<String> tokensList = new ArrayList<>();
        boolean inQuotes = false;
        boolean inSingleQuotes = false;
        StringBuilder b = new StringBuilder();
        char[] chars = sqls.toCharArray();
        int idx = 0;
        for (char c : chars) {
            char flag = 0;
            if (idx > 0) {
                flag = chars[idx - 1];
            }
            if (c == delimiter) {
                if (inQuotes) {
                    b.append(c);
                } else if (inSingleQuotes) {
                    b.append(c);
                } else {
                    if (StringUtils.isNotBlank(b)) {
                        tokensList.add(b.toString());
                        b = new StringBuilder();
                    }
                }
            } else if (c == '\"' && '\\' != flag) {
                inQuotes = !inQuotes;
                b.append(c);
            } else if (c == '\'' && '\\' != flag && !inQuotes) {
                inSingleQuotes = !inSingleQuotes;
                b.append(c);
            } else {
                b.append(c);
            }
            idx++;
        }

        if (StringUtils.isNotBlank(b)) {
            tokensList.add(b.toString());
        }

        return tokensList;
    }

    /**
     * 使用zip进行压缩
     *
     * @param str 压缩前的文本
     * @return 返回压缩后的文本
     */
    public static final String zip(String str) {
        if (str == null) {
            return null;
        }
        byte[] compressed;
        ByteArrayOutputStream out = null;
        ZipOutputStream zout = null;
        String compressedStr = null;
        try {
            out = new ByteArrayOutputStream();
            zout = new ZipOutputStream(out);
            zout.putNextEntry(new ZipEntry("0"));
            zout.write(str.getBytes());
            zout.closeEntry();
            compressed = out.toByteArray();
            compressedStr = new sun.misc.BASE64Encoder().encodeBuffer(compressed);
        } catch (IOException e) {
            compressed = null;
            compressedStr = str;
        } finally {
            if (zout != null) {
                try {
                    zout.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }
        return compressedStr;
    }
}
