package com.dsg.database.datasource.dto.source;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * S3数据源配置参数
 *
 * <AUTHOR>
 * @date 2022/7/14 15:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class S3DatasourceDTO extends BaseDatasourceDTO {

    private static final long serialVersionUID = 1L;

    /**
     * accessKey
     */
    protected String username;

    /**
     * secretKey
     */
    protected String password;

    /**
     * 域名信息
     */
    private String hostname;
}
