package com.dsg.database.datasource.dto;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * kafka数据源DTO
 *
 * <AUTHOR>
 * @date 2022/7/18 11:11
 */
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
@EqualsAndHashCode(callSuper = true)
public class KafkaDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 数据预览模式 1.earliest 2.latest
     */
    private String previewModel;

    /**
     * kafka topic 名称
     */
    private String topic;

    /**
     * 消费者组
     */
    private String groupId;

    /**
     * 最大条数
     */
    private Integer collectNum;

    /**
     * 从哪里开始消费
     */
    private String offsetReset;

    /**
     * 消费启始位置
     */
    private Long timestampOffset;

    /**
     * 最大等待时间，单位秒
     */
    private Integer maxTimeWait;

    /**
     * 分区数
     */
    @Builder.Default
    private Integer partitions = 1;

    /**
     * 复制因子
     */
    @Builder.Default
    private Short replicationFactor = 1;
}
