package com.dsg.database.datasource.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/10/9
 * @create 2022-10-09-11:15
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TopIcMetaInfoDTO {
    private String topic;
    private int partitionId;
    private long logSize;
    private int leader;
    private String isr;
    private String replicas;
    private boolean preferredLeader;
    private boolean underReplicated;
    /**
     * 修改节点的时间
     */
    private String updateTime;

    /**
     * 创建节点的时间
     */
    private String createTime;
}
