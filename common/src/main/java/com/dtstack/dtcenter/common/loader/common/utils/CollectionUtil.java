/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 20:24 2020/2/26
 * @Description：数组工具类
 */
public class CollectionUtil {
    public static String listToStr(List list) {
        StringBuilder sBuilder = new StringBuilder();
        list.stream().filter(str -> {
            if (StringUtils.isBlank((String) str)) {
                return false;
            } else {
                return true;
            }
        }).forEach(str -> {
            sBuilder.append(str).append(",");
        });
        if (sBuilder.length() > 0) {
            sBuilder.deleteCharAt(sBuilder.length() - 1);
        }
        return sBuilder.toString();
    }
}
